# Simple JavaScript Optimization
Write-Host "JavaScript Optimization Starting..." -ForegroundColor Green

# Create backup
if (-not (Test-Path "backups")) { New-Item -ItemType Directory -Path "backups" -Force }
Copy-Item "js/auth" "backups/js-auth-original" -Recurse -Force
Copy-Item "js/ui" "backups/js-ui-original" -Recurse -Force
Write-Host "Backup created" -ForegroundColor Green

# 1. Consolidate Auth Config Files
Write-Host "Consolidating auth config files..." -ForegroundColor Yellow

$configContent = @'
// Unified Firebase Configuration Module
export const firebaseConfig = {
    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
    authDomain: "mzm-gpace.firebaseapp.com",
    projectId: "mzm-gpace",
    storageBucket: "mzm-gpace.firebasestorage.app",
    messagingSenderId: "949014366726",
    appId: "1:949014366726:web:3aa05a6e133e2066c45187"
};

export const legacyFirebaseConfig = {
    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
    authDomain: "mzm-gpace.firebaseapp.com",
    databaseURL: "https://mzm-gpace-default-rtdb.asia-southeast1.firebasedatabase.app",
    projectId: "mzm-gpace",
    storageBucket: "mzm-gpace.appspot.com",
    messagingSenderId: "949014366726",
    appId: "1:949014366726:web:3aa05a6e133e2066c45187",
    measurementId: "G-5KDN1WTCQ1"
};

export function getOrCreateFirebaseApp(initializeApp) {
    try {
        if (typeof initializeApp === 'function') {
            try {
                return initializeApp(firebaseConfig);
            } catch (e) {
                if (e.code === 'app/duplicate-app') {
                    console.log("Firebase app already exists, using existing app.");
                    return initializeApp();
                }
                throw e;
            }
        } 
        else if (window.firebase) {
            if (!firebase.apps.length) {
                return firebase.initializeApp(firebaseConfig);
            }
            return firebase.apps[0];
        }
        else {
            console.error("No Firebase initialization method available");
            return null;
        }
    } catch (error) {
        console.error("Error initializing Firebase app:", error);
        return null;
    }
}
'@

Set-Content -Path "js/auth/config.js" -Value $configContent -Encoding UTF8
Write-Host "Created unified config.js" -ForegroundColor Green

# Remove redundant config file
if (Test-Path "js/auth/firebase-config.js") {
    Remove-Item "js/auth/firebase-config.js" -Force
    Write-Host "Removed redundant firebase-config.js" -ForegroundColor Green
}

# 2. Consolidate Theme Management
Write-Host "Consolidating theme management..." -ForegroundColor Yellow

$themeContent = @'
/**
 * Unified Theme Management System
 */
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'dark';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupEventListeners();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        document.body.className = theme === 'light' ? 'light-theme' : '';
        localStorage.setItem('theme', theme);
        this.currentTheme = theme;
        this.updateThemeButtons();
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }

    updateThemeButtons() {
        const lightBtns = document.querySelectorAll('.theme-btn[data-theme="light"], .light-theme');
        const darkBtns = document.querySelectorAll('.theme-btn[data-theme="dark"], .dark-theme');
        
        lightBtns.forEach(btn => {
            btn.classList.toggle('active', this.currentTheme === 'light');
        });
        
        darkBtns.forEach(btn => {
            btn.classList.toggle('active', this.currentTheme === 'dark');
        });
    }

    setupEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.theme-btn[data-theme]')) {
                const theme = e.target.getAttribute('data-theme');
                this.applyTheme(theme);
            }
            
            if (e.target.matches('.theme-toggle, .theme-switch')) {
                this.toggleTheme();
            }
        });
    }

    static getInstance() {
        if (!window.themeManager) {
            window.themeManager = new ThemeManager();
        }
        return window.themeManager;
    }
}

const themeManager = ThemeManager.getInstance();
export default ThemeManager;
window.ThemeManager = ThemeManager;
window.themeManager = themeManager;
'@

Set-Content -Path "js/ui/theme.js" -Value $themeContent -Encoding UTF8
Write-Host "Created unified theme.js" -ForegroundColor Green

# Remove redundant theme files
$themeFilesToRemove = @("theme-manager.js", "theme-manager-alt.js", "theme-script.js")
foreach ($file in $themeFilesToRemove) {
    $path = "js/ui/$file"
    if (Test-Path $path) {
        Remove-Item $path -Force
        Write-Host "Removed $file" -ForegroundColor Green
    }
}

Write-Host "JavaScript optimization completed!" -ForegroundColor Green
