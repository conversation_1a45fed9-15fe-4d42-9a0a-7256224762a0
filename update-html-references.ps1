# Update HTML References After JS/CSS Optimization
Write-Host "🔄 Updating HTML References After Optimization" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Get all HTML files in root directory
$htmlFiles = Get-ChildItem -Path "." -Filter "*.html" | Where-Object { $_.Name -notlike ".*" }

Write-Host "Found $($htmlFiles.Count) HTML files to update:" -ForegroundColor Yellow
foreach ($file in $htmlFiles) {
    Write-Host "  - $($file.Name)" -ForegroundColor White
}

Write-Host ""
Write-Host "📝 Updating JavaScript References..." -ForegroundColor Yellow
Write-Host "------------------------------------" -ForegroundColor Yellow

# JavaScript reference updates
$jsUpdates = @{
    # Auth module consolidation
    'js/auth/firebase-config\.js' = 'js/auth/config.js'
    'js/auth/firebase-auth\.js' = 'js/auth/core.js'
    
    # Theme management unification
    'js/ui/theme-manager\.js' = 'js/ui/theme.js'
    'js/ui/theme-manager-alt\.js' = 'js/ui/theme.js'
    'js/ui/theme-script\.js' = 'js/ui/theme.js'
    
    # Priority system (if we implement this)
    'js/tasks/priority-sorting\.js' = 'js/tasks/priority-core.js'
    'js/tasks/priority-utils\.js' = 'js/tasks/priority-core.js'
    
    # Core utilities (if we implement this)
    'js/core/favicon\.js' = 'js/core/utilities.js'
    'js/core/html-updater\.js' = 'js/core/utilities.js'
    'js/core/script-organizer\.js' = 'js/core/utilities.js'
}

foreach ($file in $htmlFiles) {
    Write-Host "Processing $($file.Name)..." -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $changesMade = 0
    
    foreach ($update in $jsUpdates.GetEnumerator()) {
        $oldPath = $update.Key
        $newPath = $update.Value
        
        if ($content -match $oldPath) {
            $content = $content -replace $oldPath, $newPath
            $changesMade++
            Write-Host "  ✅ Updated: $oldPath → $newPath" -ForegroundColor Green
        }
    }
    
    # Save if changes were made
    if ($changesMade -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  📁 Saved $($file.Name) with $changesMade changes" -ForegroundColor Green
    } else {
        Write-Host "  ℹ️  No JS changes needed for $($file.Name)" -ForegroundColor Blue
    }
}

Write-Host ""
Write-Host "🎨 Updating CSS References..." -ForegroundColor Yellow
Write-Host "-----------------------------" -ForegroundColor Yellow

# CSS reference updates
$cssUpdates = @{
    # Study spaces combination
    'css/study/spaces-alt\.css' = 'css/study/spaces.css'
    
    # Task display reorganization
    'css/layout/task-display\.css' = 'css/tasks/general.css'
    
    # If we had moved other files
    'css/core/theme\.css' = 'css/core/theme.css'  # No change, but ensure it's still referenced
}

foreach ($file in $htmlFiles) {
    Write-Host "Processing $($file.Name)..." -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $changesMade = 0
    
    foreach ($update in $cssUpdates.GetEnumerator()) {
        $oldPath = $update.Key
        $newPath = $update.Value
        
        if ($content -match $oldPath -and $oldPath -ne $newPath) {
            $content = $content -replace $oldPath, $newPath
            $changesMade++
            Write-Host "  ✅ Updated: $oldPath → $newPath" -ForegroundColor Green
        }
    }
    
    # Save if changes were made
    if ($changesMade -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  📁 Saved $($file.Name) with $changesMade changes" -ForegroundColor Green
    } else {
        Write-Host "  ℹ️  No CSS changes needed for $($file.Name)" -ForegroundColor Blue
    }
}

Write-Host ""
Write-Host "🔍 Verification: Checking for Broken References..." -ForegroundColor Yellow
Write-Host "--------------------------------------------------" -ForegroundColor Yellow

$brokenReferences = @()

foreach ($file in $htmlFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check for JS references
    $jsMatches = [regex]::Matches($content, 'src="([^"]*\.js)"')
    foreach ($match in $jsMatches) {
        $jsPath = $match.Groups[1].Value
        if ($jsPath -match '^(js/|\.\.\/js/)' -and -not $jsPath.StartsWith('http')) {
            $fullPath = $jsPath -replace '^\.\.\/', ''
            if (-not (Test-Path $fullPath)) {
                $brokenReferences += "$($file.Name): $jsPath"
            }
        }
    }
    
    # Check for CSS references
    $cssMatches = [regex]::Matches($content, 'href="([^"]*\.css)"')
    foreach ($match in $cssMatches) {
        $cssPath = $match.Groups[1].Value
        if ($cssPath -match '^(css/|\.\.\/css/)' -and -not $cssPath.StartsWith('http')) {
            $fullPath = $cssPath -replace '^\.\.\/', ''
            if (-not (Test-Path $fullPath)) {
                $brokenReferences += "$($file.Name): $cssPath"
            }
        }
    }
}

if ($brokenReferences.Count -gt 0) {
    Write-Host "⚠️  Found potential broken references:" -ForegroundColor Red
    foreach ($ref in $brokenReferences) {
        Write-Host "  - $ref" -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ No broken references found!" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 HTML Reference Updates Completed!" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host "✅ JavaScript references updated" -ForegroundColor White
Write-Host "✅ CSS references updated" -ForegroundColor White
Write-Host "✅ Verification completed" -ForegroundColor White
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Test all HTML pages to ensure they load correctly" -ForegroundColor White
Write-Host "2. Check browser console for any missing file errors" -ForegroundColor White
Write-Host "3. Verify functionality works as expected" -ForegroundColor White
