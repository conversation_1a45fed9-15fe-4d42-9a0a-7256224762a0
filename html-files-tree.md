# HTML Files Tree Structure

This document provides a comprehensive tree structure of all HTML files in the codebase.

## Overview
- **Total HTML Files**: 19 files
- **Main Directory**: Root level (17 files)
- **Feature Directories**: relaxed-mode/, Youtube Searcher (Not Completed)/
- **Total Lines of Code**: 13,091 lines

## Complete Tree Structure with Dependencies

```
Creating an App/
├── 404.html                              # 42 lines - Error page
│   └── JS: js/cross-tab-sync.js
├── academic-details.html                 # 381 lines - Academic information page
│   ├── CSS: css/academic-details.css, css/sideDrawer.css
│   └── JS: js/academic-details.js, js/auth.js, js/cross-tab-sync.js,
│           js/firestore.js, js/semester-management.js, js/sideDrawer.js,
│           js/subject-management.js, js/ui-utilities.js, /js/inject-header.js
├── daily-calendar.html                   # 236 lines - Calendar interface
│   ├── CSS: css/daily-calendar.css, css/sideDrawer.css, css/task-display.css, main.css
│   └── JS: js/calendarManager.js, js/cross-tab-sync.js, js/currentTaskManager.js,
│           js/firebaseAuth.js, js/sideDrawer.js, js/sleepScheduleManager.js,
│           js/timetableIntegration.js, /js/inject-header.js
├── extracted.html                        # 2,125 lines - Extracted content page
│   ├── CSS: css/extracted.css, css/sideDrawer.css
│   └── JS: js/cross-tab-sync.js, js/sideDrawer.js, /js/inject-header.js
├── flashcards.html                       # 333 lines - Flashcard study system
│   ├── CSS: css/flashcards.css, css/sideDrawer.css, grind.css, main.css
│   └── JS: js/common.js, js/cross-tab-sync.js, js/flashcards.js, js/sideDrawer.js,
│           js/sm2.js, js/storageManager.js, /js/inject-header.js
├── grind.html                            # 5,131 lines - Main application interface
│   ├── CSS: css/ai-search-response.css, css/sideDrawer.css, css/simulation-enhancer.css,
│   │        css/task-display.css, css/taskLinks.css, css/task-notes.css,
│   │        css/text-expansion.css, grind.css, main.css
│   └── JS: js/ai-latex-conversion.js, js/ai-researcher.js, js/common.js,
│           js/cross-tab-sync.js, js/currentTaskManager.js, js/energyLevels.js,
│           js/firebase-init.js, js/grind-speech-synthesis.js, js/initFirestoreData.js,
│           js/pomodoroTimer.js, js/sideDrawer.js, js/sleepTimeCalculator.js,
│           js/storageManager.js, js/taskLinks.js, js/task-notes.js,
│           js/task-notes-injector.js, js/text-expansion.js, js/userGuidance.js,
│           priority-calculator.js, /js/inject-header.js
├── index.html                            # 65 lines - Landing/home page
│   ├── CSS: css/alarm-service.css
│   └── JS: js/alarm-mini-display.js, js/alarm-service.js, js/cacheManager.js,
│           js/cross-tab-sync.js, /js/inject-header.js
├── instant-test-feedback.html            # 427 lines - Test feedback interface
│   ├── CSS: css/sideDrawer.css, css/test-feedback.css, grind.css, main.css
│   └── JS: js/api-optimization.js, js/api-settings.js, js/cross-tab-sync.js,
│           js/gemini-api.js, js/sideDrawer.js, js/test-feedback.js, /js/inject-header.js
├── landing.html                          # 1,587 lines - Main landing page
│   ├── CSS: css/sideDrawer.css, styles/main.css
│   └── JS: js/cross-tab-sync.js, js/theme-toggle.js, /js/inject-header.js
├── priority-calculator.html              # 180 lines - Priority calculation tool
│   ├── CSS: css/priority-calculator.css, css/sideDrawer.css
│   └── JS: js/common.js, js/cross-tab-sync.js, js/sideDrawer.js, priority-calculator.js
├── priority-list.html                    # 154 lines - Priority-based task lists
│   ├── CSS: css/priority-list.css, css/sideDrawer.css
│   └── JS: js/common.js, js/cross-tab-sync.js, js/priority-list-sorting.js,
│           js/priority-list-utils.js, js/priority-sync-fix.js, js/sideDrawer.js,
│           /js/inject-header.js
├── settings.html                         # 156 lines - Application settings
│   ├── CSS: css/settings.css
│   └── JS: js/app.js, js/gemini-api.js, js/quoteManager.js, js/roleModelManager.js,
│           js/soundManager.js, js/themeManager.js, js/todoistIntegration.js,
│           js/transitionManager.js, /js/inject-header.js
├── sleep-saboteurs.html                  # 212 lines - Sleep tracking interface
│   ├── CSS: css/alarm-service.css, css/sideDrawer.css, css/sleep-saboteurs.css
│   └── JS: js/alarm-service.js, js/clock-display.js, js/cross-tab-sync.js,
│           js/sideDrawer.js, js/sleep-saboteurs-init.js, js/theme-manager.js,
│           /js/inject-header.js
├── study-spaces.html                     # 366 lines - Study space management
│   ├── CSS: css/sideDrawer.css, css/study-spaces.css, main.css
│   └── JS: js/apiSettingsManager.js, js/cross-tab-sync.js, js/firebaseAuth.js,
│           js/imageAnalyzer.js, js/scheduleManager.js, js/sideDrawer.js,
│           js/studySpaceAnalyzer.js, js/studySpacesManager.js, js/timetableAnalyzer.js,
│           /js/inject-header.js, /socket.io/socket.io.js
├── subject-marks.html                    # 223 lines - Subject grades display
│   ├── CSS: css/sideDrawer.css, css/subject-marks.css
│   └── JS: js/cross-tab-sync.js, js/data-sync-integration.js, js/firebase-init.js,
│           js/firestore-global.js, js/sideDrawer.js, js/subject-marks-integration.js,
│           js/subject-marks-ui.js, /js/inject-header.js
├── tasks.html                            # 830 lines - Task management interface
│   ├── CSS: css/sideDrawer.css
│   └── JS: js/cross-tab-sync.js, js/sideDrawer.js, js/taskFilters.js,
│           js/tasksManager.js, js/todoistIntegration.js, /js/inject-header.js
├── workspace.html                        # 408 lines - Document workspace
│   ├── CSS: css/workspace.css
│   └── JS: js/cross-tab-sync.js, js/sm2.js, js/speech-recognition.js,
│           js/speech-synthesis.js, js/workspace-attachments.js, js/workspace-core.js,
│           js/workspace-document.js, js/workspaceFlashcardIntegration.js,
│           js/workspace-formatting.js, js/workspace-media.js,
│           js/workspace-tables-links.js, js/workspace-ui.js, /js/inject-header.js
├── relaxed-mode/                         # Relaxed study mode feature
│   └── index.html                        # 186 lines - Relaxed mode interface
│       ├── CSS: ../css/sideDrawer.css, ../styles/main.css, style.css
│       └── JS: ../js/common.js, ../js/cross-tab-sync.js, ../js/sideDrawer.js,
│               ../js/tasksManager.js, ../js/transitionManager.js, script.js
└── Youtube Searcher (Not Completed)/     # Incomplete YouTube integration
    └── index.html                        # 49 lines - YouTube searcher UI
        ├── CSS: styles.css
        └── JS: ../js/cross-tab-sync.js, app.js
```

## File Categories

### 🏠 Core Application Pages
- **grind.html** (5,131 lines) - Main application interface with comprehensive features
- **landing.html** (1,587 lines) - Primary landing page with app introduction
- **index.html** (65 lines) - Simple home/entry point
- **extracted.html** (2,125 lines) - Extracted content and shared components

### 📚 Academic Management
- **academic-details.html** (381 lines) - Academic information and profile
- **subject-marks.html** (223 lines) - Subject grades and performance tracking
- **instant-test-feedback.html** (427 lines) - Test result feedback system

### 📝 Task & Productivity Management
- **tasks.html** (830 lines) - Comprehensive task management interface
- **priority-calculator.html** (180 lines) - Priority calculation and ranking
- **priority-list.html** (154 lines) - Priority-based task organization

### 📅 Calendar & Scheduling
- **daily-calendar.html** (236 lines) - Calendar view and scheduling interface

### 📖 Study Tools
- **flashcards.html** (333 lines) - Flashcard creation and study system
- **study-spaces.html** (366 lines) - Study environment management
- **workspace.html** (408 lines) - Document editing and workspace

### 😴 Wellness & Health
- **sleep-saboteurs.html** (212 lines) - Sleep tracking and optimization

### ⚙️ System Pages
- **settings.html** (156 lines) - Application configuration and preferences
- **404.html** (42 lines) - Error page for missing resources

### 🎮 Special Features
- **relaxed-mode/index.html** (186 lines) - Relaxed study mode interface
- **Youtube Searcher/index.html** (49 lines) - YouTube integration (incomplete)

## Statistics by File Size

### Extra Large Files (>2000 lines)
1. **grind.html** - 5,131 lines (Main app interface)
2. **extracted.html** - 2,125 lines (Shared components)

### Large Files (1000-2000 lines)
1. **landing.html** - 1,587 lines (Landing page)

### Medium Files (200-1000 lines)
- tasks.html (830 lines)
- instant-test-feedback.html (427 lines)
- workspace.html (408 lines)
- academic-details.html (381 lines)
- study-spaces.html (366 lines)
- flashcards.html (333 lines)
- daily-calendar.html (236 lines)
- subject-marks.html (223 lines)
- sleep-saboteurs.html (212 lines)

### Small Files (100-200 lines)
- relaxed-mode/index.html (186 lines)
- priority-calculator.html (180 lines)
- settings.html (156 lines)
- priority-list.html (154 lines)

### Tiny Files (<100 lines)
- index.html (65 lines)
- Youtube Searcher/index.html (49 lines)
- 404.html (42 lines)

## File Size Distribution
- **Extra Large (>2000 lines)**: 2 files
- **Large (1000-2000 lines)**: 1 file
- **Medium (200-1000 lines)**: 9 files
- **Small (100-200 lines)**: 4 files
- **Tiny (<100 lines)**: 3 files

## Dependency Analysis

### 📊 Most Referenced Files

#### JavaScript Dependencies
- **js/cross-tab-sync.js**: Used in 18/19 HTML files (95% coverage)
- **js/sideDrawer.js**: Used in 15/19 HTML files (79% coverage)
- **/js/inject-header.js**: Used in 15/19 HTML files (79% coverage)
- **js/common.js**: Used in 5/19 HTML files
- **js/firestore.js, js/firebase-init.js**: Firebase integration files

#### CSS Dependencies
- **css/sideDrawer.css**: Used in 15/19 HTML files (79% coverage)
- **main.css**: Used in 6/19 HTML files
- **grind.css**: Used in 3/19 HTML files (main theme)

### 🔗 Dependency Patterns

#### Core Infrastructure Files
- **Cross-tab synchronization**: Nearly universal (js/cross-tab-sync.js)
- **Navigation**: Side drawer used across most pages (js/sideDrawer.js, css/sideDrawer.css)
- **Header injection**: Common header functionality (/js/inject-header.js)

#### Feature-Specific Dependencies
- **Firebase**: Academic and data-heavy pages (subject-marks, study-spaces, academic-details)
- **Task Management**: Multiple JS files for task-related functionality
- **Workspace**: Extensive dependencies for document editing capabilities
- **AI Features**: Concentrated in grind.html and instant-test-feedback.html

### 📁 File Relationship Matrix

#### Pages with Highest Dependencies
1. **grind.html**: 17 JS files, 9 CSS files (Main application hub)
2. **workspace.html**: 12 JS files, 1 CSS file (Document editor)
3. **study-spaces.html**: 10 JS files, 3 CSS files (Study management)
4. **academic-details.html**: 8 JS files, 2 CSS files (Academic tracking)

#### Pages with Minimal Dependencies
1. **404.html**: 1 JS file, 0 CSS files (Error page)
2. **Youtube Searcher/index.html**: 2 JS files, 1 CSS file (Incomplete feature)
3. **index.html**: 5 JS files, 1 CSS file (Simple landing)

### 🎯 Shared vs Specific Resources

#### Shared Resources (Used by 5+ pages)
- **CSS**: sideDrawer.css (15 pages), main.css (6 pages)
- **JS**: cross-tab-sync.js (18 pages), sideDrawer.js (15 pages), inject-header.js (15 pages)

#### Page-Specific Resources
- **grind.html**: Uses unique files like ai-researcher.js, simulation-enhancer.css
- **workspace.html**: Dedicated workspace-*.js files (8 files)
- **flashcards.html**: Specialized flashcard system files
- **test-feedback.html**: AI-powered feedback system files

## Functional Analysis

### 🎯 Primary User Interfaces
The application features multiple specialized interfaces:
- **Main App**: grind.html serves as the comprehensive dashboard
- **Landing**: landing.html provides user onboarding and feature overview
- **Task Management**: Dedicated pages for different aspects of productivity

### 📱 Feature Modularity
Each major feature has its own dedicated HTML page:
- Academic tracking and performance analysis
- Study tools (flashcards, workspaces, study spaces)
- Time and task management with priority systems
- Health and wellness tracking (sleep optimization)

### 🔧 System Architecture
- **Modular Design**: Separate pages for distinct functionalities
- **Shared Components**: extracted.html contains reusable elements
- **Error Handling**: Dedicated 404 page for better UX
- **Settings Management**: Centralized configuration interface

### 🚀 Development Status
- **Complete Features**: 17 fully implemented pages
- **In Development**: YouTube Searcher feature (incomplete)
- **Special Modes**: Relaxed mode for focused studying

---
*Generated on: $(date)*
*Tree structure created using find command and manual organization*
*File statistics generated using wc -l command*
