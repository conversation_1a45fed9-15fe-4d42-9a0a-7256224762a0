# JavaScript Restructuring Migration Script
# This script implements the complete restructuring plan from js-restructuring-plan.md

Write-Host "=== JavaScript Files Restructuring Migration ===" -ForegroundColor Green
Write-Host "Starting migration process..." -ForegroundColor Yellow

# Phase 1: Create Directory Structure
Write-Host "`n--- Phase 1: Creating Directory Structure ---" -ForegroundColor Cyan

$directories = @(
    "js/core",
    "js/auth", 
    "js/data",
    "js/tasks",
    "js/calendar",
    "js/academic",
    "js/study",
    "js/workspace",
    "js/ai",
    "js/media",
    "js/alarms",
    "js/health",
    "js/ui",
    "js/utils"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "Directory already exists: $dir" -ForegroundColor Yellow
    }
}

# Phase 2: File Moves with Git History Preservation
Write-Host "`n--- Phase 2: Moving Files (Preserving Git History) ---" -ForegroundColor Cyan

# Define all file moves according to the restructuring plan
$fileMoves = @{
    # Core Infrastructure
    "js/common.js" = "js/core/common.js"
    "js/common-header.js" = "js/core/header.js"
    "js/cross-tab-sync.js" = "js/core/sync.js"
    "js/inject-header.js" = "js/core/inject-header.js"
    "js/ui-utilities.js" = "js/core/ui-utils.js"
    "js/transitionManager.js" = "js/core/transitions.js"
    "js/add-favicon.js" = "js/core/favicon.js"
    "js/update-html-files.js" = "js/core/html-updater.js"
    "js/reorganize-scripts.js" = "js/core/script-organizer.js"
    
    # Authentication & Config
    "js/auth.js" = "js/auth/auth.js"
    "js/firebaseAuth.js" = "js/auth/firebase-auth.js"
    "js/firebase-config.js" = "js/auth/config.js"
    "js/firebaseConfig.js" = "js/auth/firebase-config.js"
    "js/firebase-init.js" = "js/auth/init.js"
    
    # Data Management
    "js/storageManager.js" = "js/data/storage.js"
    "js/indexedDB.js" = "js/data/indexed-db.js"
    "js/firestore.js" = "js/data/firestore.js"
    "js/firestore-global.js" = "js/data/firestore-global.js"
    "js/initFirestoreData.js" = "js/data/init-firestore.js"
    "js/data-loader.js" = "js/data/loader.js"
    "js/data-sync-manager.js" = "js/data/sync-manager.js"
    "js/data-sync-integration.js" = "js/data/sync-integration.js"
    "js/marks-tracking.js" = "js/data/marks-tracking.js"
    
    # Task Management
    "js/tasksManager.js" = "js/tasks/manager.js"
    "js/currentTaskManager.js" = "js/tasks/current-task.js"
    "js/taskAttachments.js" = "js/tasks/attachments.js"
    "js/taskFilters.js" = "js/tasks/filters.js"
    "js/taskLinks.js" = "js/tasks/links.js"
    "js/task-notes.js" = "js/tasks/notes.js"
    "js/task-notes-injector.js" = "js/tasks/notes-injector.js"
    "js/priority-list-sorting.js" = "js/tasks/priority-sorting.js"
    "js/priority-list-utils.js" = "js/tasks/priority-utils.js"
    "js/priority-sync-fix.js" = "js/tasks/priority-sync.js"
    "js/priority-worker-wrapper.js" = "js/tasks/priority-worker.js"
    "js/todoistIntegration.js" = "js/tasks/todoist.js"
    
    # Calendar & Scheduling
    "js/calendarManager.js" = "js/calendar/manager.js"
    "js/calendar-views.js" = "js/calendar/views.js"
    "js/scheduleManager.js" = "js/calendar/schedule.js"
    "js/timetableAnalyzer.js" = "js/calendar/timetable-analyzer.js"
    "js/timetableIntegration.js" = "js/calendar/timetable-integration.js"
    "js/pomodoroTimer.js" = "js/calendar/pomodoro.js"
    "js/pomodoroGlobal.js" = "js/calendar/pomodoro-global.js"
    "js/clock-display.js" = "js/calendar/clock.js"
    
    # Academic Management
    "js/academic-details.js" = "js/academic/details.js"
    "js/semester-management.js" = "js/academic/semester.js"
    "js/subject-management.js" = "js/academic/subjects.js"
    "js/subject-marks.js" = "js/academic/marks.js"
    "js/subject-marks-ui.js" = "js/academic/marks-ui.js"
    "js/subject-marks-integration.js" = "js/academic/marks-integration.js"
    "js/test-feedback.js" = "js/academic/test-feedback.js"
    
    # Study Tools
    "js/flashcards.js" = "js/study/flashcards.js"
    "js/flashcardManager.js" = "js/study/flashcard-manager.js"
    "js/flashcardTaskIntegration.js" = "js/study/flashcard-tasks.js"
    "js/workspaceFlashcardIntegration.js" = "js/study/workspace-flashcards.js"
    "js/sm2.js" = "js/study/spaced-repetition.js"
    "js/studySpaceAnalyzer.js" = "js/study/space-analyzer.js"
    "js/studySpacesManager.js" = "js/study/spaces-manager.js"
    "js/studySpacesFirestore.js" = "js/study/spaces-firestore.js"
    
    # Workspace & Documents
    "js/workspace-core.js" = "js/workspace/core.js"
    "js/workspace-ui.js" = "js/workspace/ui.js"
    "js/workspace-document.js" = "js/workspace/document.js"
    "js/workspace-formatting.js" = "js/workspace/formatting.js"
    "js/workspace-attachments.js" = "js/workspace/attachments.js"
    "js/workspace-media.js" = "js/workspace/media.js"
    "js/workspace-tables-links.js" = "js/workspace/tables-links.js"
    "js/fileViewer.js" = "js/workspace/file-viewer.js"
    "js/markdown-converter.js" = "js/workspace/markdown.js"
    "js/pandoc-fallback.js" = "js/workspace/pandoc.js"
    "js/text-expansion.js" = "js/workspace/text-expansion.js"
    
    # AI & APIs
    "js/ai-researcher.js" = "js/ai/researcher.js"
    "js/ai-latex-conversion.js" = "js/ai/latex-converter.js"
    "js/gemini-api.js" = "js/ai/gemini.js"
    "js/googleDriveApi.js" = "js/ai/google-drive.js"
    "js/googleGenerativeAI.js" = "js/ai/google-generative.js"
    "js/api-optimization.js" = "js/ai/api-optimizer.js"
    "js/api-settings.js" = "js/ai/api-settings.js"
    "js/apiSettingsManager.js" = "js/ai/settings-manager.js"
    "js/imageAnalyzer.js" = "js/ai/image-analyzer.js"
    
    # Media & Speech
    "js/speech-recognition.js" = "js/media/speech-recognition.js"
    "js/speech-synthesis.js" = "js/media/speech-synthesis.js"
    "js/grind-speech-synthesis.js" = "js/media/grind-speech.js"
    "js/soundManager.js" = "js/media/sound-manager.js"
    
    # Alarms & Notifications
    "js/alarm-service.js" = "js/alarms/service.js"
    "js/alarm-handler.js" = "js/alarms/handler.js"
    "js/alarm-data-service.js" = "js/alarms/data-service.js"
    "js/alarm-mini-display.js" = "js/alarms/mini-display.js"
    "js/alarm-service-worker.js" = "js/alarms/service-worker.js"
    
    # Health & Wellness
    "js/sleepScheduleManager.js" = "js/health/sleep-schedule.js"
    "js/sleepTimeCalculator.js" = "js/health/sleep-calculator.js"
    "js/sleep-saboteurs-init.js" = "js/health/sleep-saboteurs.js"
    "js/energyLevels.js" = "js/health/energy-levels.js"
    "js/energyHologram.js" = "js/health/energy-hologram.js"
    
    # UI & Theming
    "js/sideDrawer.js" = "js/ui/side-drawer.js"
    "js/theme-manager.js" = "js/ui/theme-manager.js"
    "js/themeManager.js" = "js/ui/theme-manager-alt.js"
    "js/userGuidance.js" = "js/ui/user-guidance.js"
    "js/simulation-enhancer.js" = "js/ui/simulation-enhancer.js"
    
    # Utilities & Helpers
    "js/quoteManager.js" = "js/utils/quotes.js"
    "js/recipeManager.js" = "js/utils/recipes.js"
    "js/roleModelManager.js" = "js/utils/role-models.js"
    "js/weightage-connector.js" = "js/utils/weightage.js"
}

# Execute file moves
$moveCount = 0
$errorCount = 0

foreach ($move in $fileMoves.GetEnumerator()) {
    $source = $move.Key
    $destination = $move.Value
    
    if (Test-Path $source) {
        try {
            # Use git mv to preserve history if in git repo, otherwise use Move-Item
            if (Test-Path ".git") {
                $result = git mv $source $destination 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "Git moved: $source -> $destination" -ForegroundColor Green
                    $moveCount++
                } else {
                    Write-Host "Git move failed: $source -> $destination ($result)" -ForegroundColor Red
                    # Fallback to regular move
                    Move-Item -Path $source -Destination $destination -Force
                    Write-Host "Fallback moved: $source -> $destination" -ForegroundColor Yellow
                    $moveCount++
                }
            } else {
                Move-Item -Path $source -Destination $destination -Force
                Write-Host "Moved: $source -> $destination" -ForegroundColor Green
                $moveCount++
            }
        } catch {
            Write-Host "Error moving $source -> $destination : $($_.Exception.Message)" -ForegroundColor Red
            $errorCount++
        }
    } else {
        Write-Host "Source file not found: $source" -ForegroundColor Yellow
    }
}

Write-Host "`nPhase 2 Complete: $moveCount files moved, $errorCount errors" -ForegroundColor Cyan
