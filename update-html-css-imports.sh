#!/bin/bash

# HTML CSS Import Path Update Script
# This script updates all HTML files with new CSS import paths

echo "Starting HTML CSS import path updates..."

# Create a mapping of old paths to new paths
declare -A css_path_mappings=(
    # Core Styles
    ["grind.css"]="css/core/theme.css"
    ["css/extracted.css"]="css/core/shared.css"
    ["styles/main.css"]="css/core/main.css"
    ["main.css"]="css/core/main.css"
    ["styles/index.css"]="css/core/index.css"
    ["css/compact-style.css"]="css/core/compact.css"
    ["css/notification.css"]="css/core/notifications.css"
    
    # Navigation & Layout
    ["css/sideDrawer.css"]="css/layout/side-drawer.css"
    ["css/task-display.css"]="css/layout/task-display.css"
    
    # Academic Features
    ["css/academic-details.css"]="css/academic/details.css"
    ["css/subject-marks.css"]="css/academic/marks.css"
    ["css/test-feedback.css"]="css/academic/test-feedback.css"
    
    # Task Management
    ["css/task-notes.css"]="css/tasks/notes.css"
    ["css/taskLinks.css"]="css/tasks/links.css"
    ["css/priority-list.css"]="css/tasks/priority-list.css"
    ["css/priority-calculator.css"]="css/tasks/priority-calculator.css"
    ["styles/tasks.css"]="css/tasks/general.css"
    
    # Calendar & Scheduling
    ["css/daily-calendar.css"]="css/calendar/daily.css"
    ["styles/calendar.css"]="css/calendar/general.css"
    
    # Study Tools
    ["css/flashcards.css"]="css/study/flashcards.css"
    ["css/study-spaces.css"]="css/study/spaces.css"
    ["styles/study-spaces.css"]="css/study/spaces-alt.css"
    
    # Workspace & Documents
    ["css/workspace.css"]="css/workspace/editor.css"
    ["css/text-expansion.css"]="css/workspace/text-expansion.css"
    
    # AI & Advanced Features
    ["css/ai-search-response.css"]="css/ai/search-response.css"
    ["css/simulation-enhancer.css"]="css/ai/simulation.css"
    
    # Alarms & Notifications
    ["css/alarm-service.css"]="css/alarms/service.css"
    
    # Health & Wellness
    ["css/sleep-saboteurs.css"]="css/health/sleep-tracking.css"
    
    # Settings & Configuration
    ["css/settings.css"]="css/settings/general.css"
)

# Function to update a single HTML file
update_html_file() {
    local file="$1"
    local temp_file="${file}.tmp"
    local changes=0
    
    echo "Updating: $file"
    
    # Copy original file to temp
    cp "$file" "$temp_file"
    
    # Apply all CSS path mappings
    for old_path in "${!css_path_mappings[@]}"; do
        new_path="${css_path_mappings[$old_path]}"
        
        # Escape special characters for sed
        old_escaped=$(printf '%s\n' "$old_path" | sed 's/[[\.*^$()+?{|]/\\&/g')
        new_escaped=$(printf '%s\n' "$new_path" | sed 's/[[\.*^$()+?{|]/\\&/g')
        
        # Replace in temp file - handle both href and @import patterns
        if sed -i "s|href=[\"']${old_escaped}[\"']|href=\"${new_escaped}\"|g" "$temp_file" 2>/dev/null; then
            # Check if changes were made
            if ! cmp -s "$file" "$temp_file"; then
                changes=$((changes + 1))
            fi
        fi
        
        # Also handle @import statements in style tags
        if sed -i "s|@import [\"']${old_escaped}[\"']|@import \"${new_escaped}\"|g" "$temp_file" 2>/dev/null; then
            if ! cmp -s "$file" "$temp_file"; then
                changes=$((changes + 1))
            fi
        fi
    done
    
    # If changes were made, replace original with temp
    if [ $changes -gt 0 ]; then
        mv "$temp_file" "$file"
        echo "  ✓ Updated CSS import paths"
    else
        rm "$temp_file"
        echo "  - No changes needed"
    fi
}

# Update all HTML files in root directory
echo "Updating root HTML files..."
for file in *.html; do
    if [ -f "$file" ]; then
        update_html_file "$file"
    fi
done

# Update HTML files in subdirectories
echo "Updating subdirectory HTML files..."
find . -name "*.html" -not -path "./.*" -not -name "*.html" | while read -r file; do
    if [[ "$file" != "./"*.html ]]; then
        update_html_file "$file"
    fi
done

echo ""
echo "HTML CSS import path updates completed!"
echo "All CSS import paths have been updated to use the new modular structure."
