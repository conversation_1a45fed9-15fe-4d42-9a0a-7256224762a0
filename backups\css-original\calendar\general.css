/* Calendar Styles */
.calendar-container {
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    max-width: 1200px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.date-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.date-controls h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
    min-width: 250px;
    text-align: center;
}

.time-settings {
    display: flex;
    align-items: center;
    gap: 10px;
}

.time-settings label {
    color: #666;
    font-size: 0.9em;
}

.time-settings input[type="time"] {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
}

.settings-button {
    padding: 6px 12px;
    background: #1976d2;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.settings-button:hover {
    background: #1565c0;
}

.nav-button {
    background: none;
    border: none;
    color: #1976d2;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-button:hover {
    background: #e3f2fd;
}

.calendar-grid-container {
    display: flex;
    margin-top: 20px;
    position: relative;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    height: 1440px; /* 24 hours * 60px per hour */
}

.time-axis {
    width: 60px;
    position: sticky;
    left: 0;
    background: white;
    z-index: 1;
    border-right: 1px solid #e0e0e0;
    height: 100%;
}

.time-label {
    position: absolute;
    color: #666;
    font-size: 0.8em;
    padding-right: 8px;
    transform: translateY(-50%);
    right: 0;
    white-space: nowrap;
}

.time-label.hour-label {
    font-weight: bold;
    color: #333;
}

.time-label.minute-label {
    font-size: 0.7em;
    opacity: 0.7;
}

.calendar-grid {
    position: relative;
    flex-grow: 1;
    background: #f8f9fa;
    border-left: 1px solid #dee2e6;
    height: 100%;
}

.grid-line {
    position: absolute;
    left: 0;
    right: 0;
    height: 1px;
    pointer-events: none;
}

.grid-line.hour-line {
    background-color: rgba(0, 0, 0, 0.1);
}

.grid-line.minute-line {
    background-color: rgba(0, 0, 0, 0.05);
}

.task-block {
    position: absolute;
    background-color: var(--primary-color);
    border-radius: 4px;
    padding: 4px 8px;
    color: white;
    font-size: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-block:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 3;
}

.task-time {
    font-size: 11px;
    opacity: 0.9;
    white-space: nowrap;
    margin-bottom: 2px;
}

.task-title {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.task-block.class {
    background-color: var(--primary-color);
}

.task-block.study {
    background-color: var(--secondary-color);
}

.task-block.break {
    background-color: #4CAF50;
}

.task-block.free {
    background-color: #9E9E9E;
    opacity: 0.8;
}

/* Overlapping events styles */
.task-block.overlapping {
    margin-right: 10px;
}

.task-block.overlapping:hover {
    width: auto !important;
    z-index: 10;
}

/* Current time indicator */
.current-time-indicator {
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #ff4444;
    z-index: 100;
}

.current-time-indicator::before {
    content: '';
    position: absolute;
    left: -5px;
    top: -4px;
    width: 10px;
    height: 10px;
    background-color: #ff4444;
    border-radius: 50%;
}

.current-time-label {
    position: absolute;
    left: -55px;
    top: -10px;
    background-color: #ff4444;
    color: white;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 11px;
    transform: translateY(-50%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .time-axis {
        width: 50px;
    }

    .time-label {
        font-size: 0.7em;
    }

    .task-block {
        font-size: 11px;
    }
}

/* Selection and creation styles */
.selection-preview {
    background-color: rgba(var(--primary-color-rgb), 0.3);
    border: 2px dashed var(--primary-color);
}

.task-block.creating {
    background-color: rgba(var(--primary-color-rgb), 0.3);
    border: 2px dashed var(--primary-color);
    pointer-events: none;
}

.event-block {
    position: absolute;
    width: calc(100% - 10px);
    left: 5px;
    z-index: 1;
    border-radius: 4px;
    padding: 5px;
    font-size: 0.9em;
    transition: all 0.2s ease;
    user-select: none;
}

.event-block:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.event-content {
    height: 100%;
    overflow: hidden;
}

.event-title {
    font-weight: bold;
    margin-bottom: 2px;
}

.event-time {
    font-size: 0.8em;
    opacity: 0.8;
}

.resize-handle {
    position: absolute;
    left: 0;
    right: 0;
    height: 6px;
    cursor: row-resize;
}

.resize-handle.top {
    top: -3px;
}

.resize-handle.bottom {
    bottom: -3px;
}

.event-editor {
    position: absolute;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 16px;
    min-width: 300px;
    z-index: 1000;
}

.event-editor input[type="text"],
.event-editor input[type="time"],
.event-editor input[type="date"],
.event-editor select {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.event-time-inputs,
.event-date-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.event-color-selector {
    display: flex;
    gap: 8px;
    margin: 10px 0;
}

.color-option {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    border-color: #000;
    transform: scale(1.1);
}

.color-option.blue { background-color: #4285f4; }
.color-option.green { background-color: #34a853; }
.color-option.yellow { background-color: #fbbc04; }
.color-option.red { background-color: #ea4335; }

.recurrence-options {
    margin: 15px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.recurrence-details {
    margin-top: 10px;
    padding-left: 20px;
}

.recurrence-end {
    margin-top: 10px;
}

.edit-options {
    display: flex;
    gap: 8px;
    align-items: center;
}

.edit-series-option {
    min-width: 120px;
}

.editor-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    gap: 8px;
}

.editor-buttons button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.editor-buttons button:hover {
    opacity: 0.9;
}

.save-btn {
    background: #4285f4;
    color: white;
}

.delete-btn {
    background: #ea4335;
    color: white;
}

.cancel-btn {
    background: #f1f3f4;
    color: #3c4043;
}

.class-block {
    background-color: #4285f4;
    color: white;
    border: 1px solid #3367d6;
}

.study-block {
    background-color: #34a853;
    color: white;
    border: 1px solid #2d8745;
}

.break-block {
    background-color: #fbbc04;
    color: white;
    border: 1px solid #f9ab00;
}

.free-block {
    background-color: #ea4335;
    color: white;
    border: 1px solid #d33828;
    opacity: 0.8;
}

.calendar-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.legend-color.class-block {
    background: #4285f4;
}

.legend-color.free-block {
    background: #34a853;
    border: 1px dashed #2d8745;
}

.current-task-header {
    background-color: var(--primary-color);
    color: var(--text-color);
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.current-task-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.current-active-task {
    border: 2px solid var(--accent-color) !important;
    box-shadow: 0 0 8px rgba(var(--accent-color-rgb), 0.4) !important;
    transform: scale(1.02);
    transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-header {
        flex-direction: column;
        gap: 15px;
    }

    .time-settings {
        flex-wrap: wrap;
        justify-content: center;
    }

    .date-controls {
        width: 100%;
        justify-content: center;
    }
}
