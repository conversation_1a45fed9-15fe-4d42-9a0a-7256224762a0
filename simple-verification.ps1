# Simple verification script
Write-Host "Authentication Fix Verification" -ForegroundColor Green

$files = @("grind.html", "tasks.html", "study-spaces.html", "subject-marks.html")

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Checking $file..." -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        
        $hasSideDrawer = $content -match 'side-drawer\.js'
        $hasSignIn = $content -match 'window\.signInWithGoogle'
        $hasSignOut = $content -match 'window\.signOutUser'
        
        Write-Host "  Side Drawer: $hasSideDrawer"
        Write-Host "  SignIn: $hasSignIn" 
        Write-Host "  SignOut: $hasSignOut"
        
        if ($hasSideDrawer -and $hasSignIn -and $hasSignOut) {
            Write-Host "  Status: OK" -ForegroundColor Green
        } elseif ($hasSideDrawer) {
            Write-Host "  Status: NEEDS ATTENTION" -ForegroundColor Red
        } else {
            Write-Host "  Status: NO SIDE DRAWER" -ForegroundColor Blue
        }
        Write-Host ""
    }
}

Write-Host "Verification complete!" -ForegroundColor Green
