# Simple authentication fix script
Write-Host "Fixing authentication function exposure..." -ForegroundColor Green

# Files that need authentication setup
$files = @(
    "tasks.html",
    "priority-list.html", 
    "flashcards.html",
    "workspace.html",
    "study-spaces.html",
    "subject-marks.html",
    "academic-details.html",
    "daily-calendar.html",
    "settings.html",
    "sleep-saboteurs.html",
    "instant-test-feedback.html",
    "priority-calculator.html",
    "extracted.html"
)

$authSetup = @'
    <!-- Authentication Setup for Side Drawer -->
    <script type="module">
        import { auth, signInWithGoogle, signOutUser } from './js/auth/auth.js';
        window.auth = auth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;
        console.log('Auth functions loaded:', !!window.signInWithGoogle, !!window.signOutUser);
    </script>
'@

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Processing $file..." -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        
        if ($content -match 'side-drawer\.js') {
            Write-Host "  Found side-drawer.js" -ForegroundColor Green
            
            # Check if auth setup is missing
            if ($content -notmatch "window\.signInWithGoogle") {
                Write-Host "  Adding auth setup..." -ForegroundColor Cyan
                
                # Insert auth setup before side-drawer script
                $pattern = '(\s*<script[^>]*src="[^"]*side-drawer\.js"[^>]*></script>)'
                $replacement = $authSetup + "`r`n" + '$1'
                $content = $content -replace $pattern, $replacement
                
                Set-Content -Path $file -Value $content -Encoding UTF8
                Write-Host "  Updated $file" -ForegroundColor Green
            } else {
                Write-Host "  Auth setup already present" -ForegroundColor Blue
            }
        } else {
            Write-Host "  No side-drawer.js found" -ForegroundColor Yellow
        }
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
    }
}

Write-Host "Done!" -ForegroundColor Green
