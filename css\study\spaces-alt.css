/* Study Spaces Page Styles */
.setup-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.study-spaces-section,
.schedule-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-description {
    color: #666;
    margin-bottom: 2rem;
}

/* Study Spaces Grid */
.study-spaces-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.study-space-upload {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.study-space-upload:hover {
    border-color: var(--primary-color);
}

.upload-area {
    text-align: center;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    background: #f8f9fa;
}

.upload-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.space-details {
    margin-top: 1rem;
}

.location-input {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.amenities {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
}

/* Schedule Section */
.time-range {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.time-input {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.time-picker {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.time-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1.1rem;
}

.buffer-select {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.buffer-select select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Timetable Upload */
.timetable-upload {
    margin-top: 2rem;
    text-align: center;
}

.timetable-upload .upload-area {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 3rem 2rem;
    margin: 1rem 0;
}

.upload-hint {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.manual-entry {
    margin-top: 1rem;
}

.secondary-button {
    background: none;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.secondary-button:hover {
    background: var(--primary-color);
    color: white;
}

/* Action Buttons */
.actions {
    text-align: center;
    margin-top: 3rem;
}

.save-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 3rem;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-button:hover {
    background: #357ABD;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Navigation Styles */
.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.nav-links {
    display: flex;
    gap: 1.5rem;
}

.nav-link {
    color: var(--text-color);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: rgba(74, 144, 226, 0.1);
    color: var(--primary-color);
}

.nav-link.active {
    background: var(--primary-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .setup-container {
        padding: 1rem;
    }

    .time-range {
        grid-template-columns: 1fr;
    }

    .study-spaces-grid {
        grid-template-columns: 1fr;
    }
}
