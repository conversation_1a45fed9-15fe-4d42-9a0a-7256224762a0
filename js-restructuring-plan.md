# JavaScript Files Restructuring Plan

## Overview
This document proposes a comprehensive categorization and modularization plan for the 108 JavaScript files currently scattered across the codebase. The goal is to create a logical, maintainable, and scalable file structure.

## Current vs Proposed Structure

### Current Issues
- **Flat structure**: 87 files in single `js/` directory
- **Inconsistent naming**: Mixed conventions (camelCase, kebab-case)
- **Unclear dependencies**: Related files scattered throughout
- **Maintenance difficulty**: Hard to locate and organize related functionality

### Proposed Benefits
- **Logical grouping**: Related functionality organized together
- **Clear dependencies**: Module boundaries and relationships
- **Easier maintenance**: Predictable file locations
- **Scalability**: Room for future growth
- **Better imports**: Cleaner import paths

## Detailed Restructuring Plan

### 📁 **Core Infrastructure** (`js/core/`)
**Purpose**: Essential system-wide functionality

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/common.js` | `js/core/common.js` | Shared utilities |
| `js/common-header.js` | `js/core/header.js` | Header management |
| `js/cross-tab-sync.js` | `js/core/sync.js` | Cross-tab synchronization |
| `js/inject-header.js` | `js/core/inject-header.js` | Header injection |
| `js/ui-utilities.js` | `js/core/ui-utils.js` | UI helper functions |
| `js/transitionManager.js` | `js/core/transitions.js` | Page transitions |
| `js/add-favicon.js` | `js/core/favicon.js` | Favicon management |
| `js/update-html-files.js` | `js/core/html-updater.js` | HTML file updates |
| `js/reorganize-scripts.js` | `js/core/script-organizer.js` | Script organization |

### 🔐 **Authentication & Config** (`js/auth/`)
**Purpose**: Authentication, configuration, and Firebase setup

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/auth.js` | `js/auth/auth.js` | Main authentication |
| `js/firebaseAuth.js` | `js/auth/firebase-auth.js` | Firebase authentication |
| `js/firebase-config.js` | `js/auth/config.js` | Firebase configuration |
| `js/firebaseConfig.js` | `js/auth/firebase-config.js` | Firebase config (duplicate) |
| `js/firebase-init.js` | `js/auth/init.js` | Firebase initialization |

### 💾 **Data Management** (`js/data/`)
**Purpose**: Data storage, synchronization, and management

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/storageManager.js` | `js/data/storage.js` | Storage management |
| `js/indexedDB.js` | `js/data/indexed-db.js` | IndexedDB operations |
| `js/firestore.js` | `js/data/firestore.js` | Firestore operations |
| `js/firestore-global.js` | `js/data/firestore-global.js` | Global Firestore |
| `js/initFirestoreData.js` | `js/data/init-firestore.js` | Firestore initialization |
| `js/data-loader.js` | `js/data/loader.js` | Data loading |
| `js/data-sync-manager.js` | `js/data/sync-manager.js` | Sync management |
| `js/data-sync-integration.js` | `js/data/sync-integration.js` | Sync integration |
| `js/marks-tracking.js` | `js/data/marks-tracking.js` | Marks data tracking |

### 📝 **Task Management** (`js/tasks/`)
**Purpose**: Task creation, management, and organization

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/tasksManager.js` | `js/tasks/manager.js` | Main task management |
| `js/currentTaskManager.js` | `js/tasks/current-task.js` | Current task handling |
| `js/taskAttachments.js` | `js/tasks/attachments.js` | Task attachments |
| `js/taskFilters.js` | `js/tasks/filters.js` | Task filtering |
| `js/taskLinks.js` | `js/tasks/links.js` | Task linking |
| `js/task-notes.js` | `js/tasks/notes.js` | Task notes |
| `js/task-notes-injector.js` | `js/tasks/notes-injector.js` | Notes injection |
| `js/priority-list-sorting.js` | `js/tasks/priority-sorting.js` | Priority sorting |
| `js/priority-list-utils.js` | `js/tasks/priority-utils.js` | Priority utilities |
| `js/priority-sync-fix.js` | `js/tasks/priority-sync.js` | Priority synchronization |
| `js/priority-worker-wrapper.js` | `js/tasks/priority-worker.js` | Priority worker |
| `js/todoistIntegration.js` | `js/tasks/todoist.js` | Todoist integration |

### 📅 **Calendar & Scheduling** (`js/calendar/`)
**Purpose**: Calendar, scheduling, and time management

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/calendarManager.js` | `js/calendar/manager.js` | Calendar management |
| `js/calendar-views.js` | `js/calendar/views.js` | Calendar views |
| `js/scheduleManager.js` | `js/calendar/schedule.js` | Schedule management |
| `js/timetableAnalyzer.js` | `js/calendar/timetable-analyzer.js` | Timetable analysis |
| `js/timetableIntegration.js` | `js/calendar/timetable-integration.js` | Timetable integration |
| `js/pomodoroTimer.js` | `js/calendar/pomodoro.js` | Pomodoro timer |
| `js/pomodoroGlobal.js` | `js/calendar/pomodoro-global.js` | Global Pomodoro state |
| `js/clock-display.js` | `js/calendar/clock.js` | Clock display |

### 🎓 **Academic Management** (`js/academic/`)
**Purpose**: Academic tracking, subjects, and educational features

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/academic-details.js` | `js/academic/details.js` | Academic details |
| `js/semester-management.js` | `js/academic/semester.js` | Semester management |
| `js/subject-management.js` | `js/academic/subjects.js` | Subject management |
| `js/subject-marks.js` | `js/academic/marks.js` | Subject marks |
| `js/subject-marks-ui.js` | `js/academic/marks-ui.js` | Marks UI |
| `js/subject-marks-integration.js` | `js/academic/marks-integration.js` | Marks integration |
| `js/test-feedback.js` | `js/academic/test-feedback.js` | Test feedback |

### 📚 **Study Tools** (`js/study/`)
**Purpose**: Study aids, flashcards, and learning tools

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/flashcards.js` | `js/study/flashcards.js` | Flashcard system |
| `js/flashcardManager.js` | `js/study/flashcard-manager.js` | Flashcard management |
| `js/flashcardTaskIntegration.js` | `js/study/flashcard-tasks.js` | Flashcard-task integration |
| `js/workspaceFlashcardIntegration.js` | `js/study/workspace-flashcards.js` | Workspace flashcards |
| `js/sm2.js` | `js/study/spaced-repetition.js` | Spaced repetition algorithm |
| `js/studySpaceAnalyzer.js` | `js/study/space-analyzer.js` | Study space analysis |
| `js/studySpacesManager.js` | `js/study/spaces-manager.js` | Study spaces management |
| `js/studySpacesFirestore.js` | `js/study/spaces-firestore.js` | Study spaces data |

### 📄 **Workspace & Documents** (`js/workspace/`)
**Purpose**: Document editing, workspace management

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/workspace-core.js` | `js/workspace/core.js` | Core workspace |
| `js/workspace-ui.js` | `js/workspace/ui.js` | Workspace UI |
| `js/workspace-document.js` | `js/workspace/document.js` | Document handling |
| `js/workspace-formatting.js` | `js/workspace/formatting.js` | Text formatting |
| `js/workspace-attachments.js` | `js/workspace/attachments.js` | File attachments |
| `js/workspace-media.js` | `js/workspace/media.js` | Media handling |
| `js/workspace-tables-links.js` | `js/workspace/tables-links.js` | Tables and links |
| `js/fileViewer.js` | `js/workspace/file-viewer.js` | File viewing |
| `js/markdown-converter.js` | `js/workspace/markdown.js` | Markdown conversion |
| `js/pandoc-fallback.js` | `js/workspace/pandoc.js` | Pandoc fallback |
| `js/text-expansion.js` | `js/workspace/text-expansion.js` | Text expansion |

### 🤖 **AI & APIs** (`js/ai/`)
**Purpose**: AI integration, external APIs, and intelligent features

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/ai-researcher.js` | `js/ai/researcher.js` | AI research functionality |
| `js/ai-latex-conversion.js` | `js/ai/latex-converter.js` | LaTeX conversion |
| `js/gemini-api.js` | `js/ai/gemini.js` | Gemini API |
| `js/googleDriveApi.js` | `js/ai/google-drive.js` | Google Drive API |
| `js/googleGenerativeAI.js` | `js/ai/google-generative.js` | Google Generative AI |
| `js/api-optimization.js` | `js/ai/api-optimizer.js` | API optimization |
| `js/api-settings.js` | `js/ai/api-settings.js` | API settings |
| `js/apiSettingsManager.js` | `js/ai/settings-manager.js` | API settings management |
| `js/imageAnalyzer.js` | `js/ai/image-analyzer.js` | Image analysis |

### 🎵 **Media & Speech** (`js/media/`)
**Purpose**: Audio, speech, and multimedia features

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/speech-recognition.js` | `js/media/speech-recognition.js` | Speech recognition |
| `js/speech-synthesis.js` | `js/media/speech-synthesis.js` | Speech synthesis |
| `js/grind-speech-synthesis.js` | `js/media/grind-speech.js` | Grind speech features |
| `js/soundManager.js` | `js/media/sound-manager.js` | Sound management |

### ⏰ **Alarms & Notifications** (`js/alarms/`)
**Purpose**: Alarm system, notifications, and alerts

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/alarm-service.js` | `js/alarms/service.js` | Alarm service |
| `js/alarm-handler.js` | `js/alarms/handler.js` | Alarm handling |
| `js/alarm-data-service.js` | `js/alarms/data-service.js` | Alarm data |
| `js/alarm-mini-display.js` | `js/alarms/mini-display.js` | Mini alarm display |
| `js/alarm-service-worker.js` | `js/alarms/service-worker.js` | Alarm service worker |

### 😴 **Health & Wellness** (`js/health/`)
**Purpose**: Sleep tracking, energy management, wellness features

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/sleepScheduleManager.js` | `js/health/sleep-schedule.js` | Sleep schedule |
| `js/sleepTimeCalculator.js` | `js/health/sleep-calculator.js` | Sleep calculations |
| `js/sleep-saboteurs-init.js` | `js/health/sleep-saboteurs.js` | Sleep saboteurs |
| `js/energyLevels.js` | `js/health/energy-levels.js` | Energy tracking |
| `js/energyHologram.js` | `js/health/energy-hologram.js` | Energy visualization |

### 🎨 **UI & Theming** (`js/ui/`)
**Purpose**: User interface, theming, and visual components

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/sideDrawer.js` | `js/ui/side-drawer.js` | Side navigation |
| `js/theme-manager.js` | `js/ui/theme-manager.js` | Theme management |
| `js/themeManager.js` | `js/ui/theme-manager-alt.js` | Alternative theme manager |
| `js/userGuidance.js` | `js/ui/user-guidance.js` | User guidance |
| `js/simulation-enhancer.js` | `js/ui/simulation-enhancer.js` | Simulation UI |

### 🔧 **Utilities & Helpers** (`js/utils/`)
**Purpose**: Utility functions, helpers, and miscellaneous tools

| Current Path | New Path | Purpose |
|--------------|----------|---------|
| `js/quoteManager.js` | `js/utils/quotes.js` | Quote management |
| `js/recipeManager.js` | `js/utils/recipes.js` | Recipe management |
| `js/roleModelManager.js` | `js/utils/role-models.js` | Role model management |
| `js/weightage-connector.js` | `js/utils/weightage.js` | Weightage calculations |

## Root Level Files (No Change)
These files remain at root level due to their special nature:

| Current Path | New Path | Reason |
|--------------|----------|---------|
| `priority-calculator.js` | `priority-calculator.js` | Standalone calculator |
| `priority-calculator-with-worker.js` | `priority-calculator-with-worker.js` | Worker version |
| `server.js` | `server.js` | Main server file |
| `test-worker.js` | `test-worker.js` | Test worker |
| `worker.js` | `worker.js` | Main worker |

## Other Directories (No Change)
- `public/` - Public assets and service workers
- `server/` - Server-side code
- `workers/` - Web workers
- `scripts/` - Build and utility scripts
- `relaxed-mode/` - Feature-specific module
- `Youtube Searcher (Not Completed)/` - Incomplete feature

## Implementation Strategy

### Phase 1: Create Directory Structure
```bash
mkdir -p js/{core,auth,data,tasks,calendar,academic,study,workspace,ai,media,alarms,health,ui,utils}
```

### Phase 2: Move Files (with git mv for history preservation)
```bash
# Example moves
git mv js/common.js js/core/common.js
git mv js/auth.js js/auth/auth.js
git mv js/tasksManager.js js/tasks/manager.js
# ... continue for all files
```

### Phase 3: Update Import Paths
- Update all HTML files with new import paths
- Update any internal module imports
- Update build scripts and configurations

### Phase 4: Create Index Files
Create `index.js` files in each directory to export main functionality:
```javascript
// js/tasks/index.js
export { default as TaskManager } from './manager.js';
export { default as TaskFilters } from './filters.js';
// ... other exports
```

## Benefits of This Structure

### 🎯 **Improved Organization**
- **Logical grouping**: Related files are together
- **Clear boundaries**: Each module has a specific purpose
- **Predictable locations**: Easy to find files

### 🔧 **Better Maintainability**
- **Easier debugging**: Related code is co-located
- **Simpler refactoring**: Clear module boundaries
- **Reduced conflicts**: Less chance of naming collisions

### 📈 **Enhanced Scalability**
- **Room for growth**: Each category can expand independently
- **Modular architecture**: Easy to add new features
- **Clear dependencies**: Better understanding of relationships

### 👥 **Developer Experience**
- **Faster navigation**: Intuitive file locations
- **Better IDE support**: Improved autocomplete and navigation
- **Clearer imports**: More descriptive import paths

## HTML Import Path Updates Required

### Files Requiring Path Updates

Based on the dependency analysis from `html-files-tree.md`, the following HTML files will need their import paths updated:

#### **404.html**
```diff
- <script type="module" src="js/cross-tab-sync.js"></script>
+ <script type="module" src="js/core/sync.js"></script>
```

#### **academic-details.html**
```diff
- <script src="js/academic-details.js"></script>
+ <script src="js/academic/details.js"></script>
- <script src="js/auth.js"></script>
+ <script src="js/auth/auth.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/firestore.js"></script>
+ <script src="js/data/firestore.js"></script>
- <script src="js/semester-management.js"></script>
+ <script src="js/academic/semester.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/subject-management.js"></script>
+ <script src="js/academic/subjects.js"></script>
- <script src="js/ui-utilities.js"></script>
+ <script src="js/core/ui-utils.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **daily-calendar.html**
```diff
- <script src="js/calendarManager.js"></script>
+ <script src="js/calendar/manager.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/currentTaskManager.js"></script>
+ <script src="js/tasks/current-task.js"></script>
- <script src="js/firebaseAuth.js"></script>
+ <script src="js/auth/firebase-auth.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/sleepScheduleManager.js"></script>
+ <script src="js/health/sleep-schedule.js"></script>
- <script src="js/timetableIntegration.js"></script>
+ <script src="js/calendar/timetable-integration.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **extracted.html**
```diff
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **flashcards.html**
```diff
- <script src="js/common.js"></script>
+ <script src="js/core/common.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/flashcards.js"></script>
+ <script src="js/study/flashcards.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/sm2.js"></script>
+ <script src="js/study/spaced-repetition.js"></script>
- <script src="js/storageManager.js"></script>
+ <script src="js/data/storage.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **grind.html** (Major updates required)
```diff
- <script defer src="js/ai-latex-conversion.js"></script>
+ <script defer src="js/ai/latex-converter.js"></script>
- <script type="module" src="js/ai-researcher.js"></script>
+ <script type="module" src="js/ai/researcher.js"></script>
- <script type="module" src="js/common.js"></script>
+ <script type="module" src="js/core/common.js"></script>
- <script type="module" src="js/cross-tab-sync.js"></script>
+ <script type="module" src="js/core/sync.js"></script>
- <script defer src="js/currentTaskManager.js"></script>
+ <script defer src="js/tasks/current-task.js"></script>
- <script defer src="js/energyLevels.js"></script>
+ <script defer src="js/health/energy-levels.js"></script>
- <script type="module" src="js/firebase-init.js"></script>
+ <script type="module" src="js/auth/init.js"></script>
- <script defer src="js/grind-speech-synthesis.js"></script>
+ <script defer src="js/media/grind-speech.js"></script>
- <script type="module" src="js/initFirestoreData.js"></script>
+ <script type="module" src="js/data/init-firestore.js"></script>
- <script defer src="js/pomodoroTimer.js"></script>
+ <script defer src="js/calendar/pomodoro.js"></script>
- <script defer src="js/sideDrawer.js"></script>
+ <script defer src="js/ui/side-drawer.js"></script>
- <script defer src="js/sleepTimeCalculator.js"></script>
+ <script defer src="js/health/sleep-calculator.js"></script>
- <script defer src="js/storageManager.js"></script>
+ <script defer src="js/data/storage.js"></script>
- <script defer src="js/taskLinks.js"></script>
+ <script defer src="js/tasks/links.js"></script>
- <script defer src="js/task-notes.js"></script>
+ <script defer src="js/tasks/notes.js"></script>
- <script defer src="js/task-notes-injector.js"></script>
+ <script defer src="js/tasks/notes-injector.js"></script>
- <script src="js/text-expansion.js"></script>
+ <script src="js/workspace/text-expansion.js"></script>
- <script type="module" src="js/userGuidance.js"></script>
+ <script type="module" src="js/ui/user-guidance.js"></script>
- <script defer src="/js/inject-header.js"></script>
+ <script defer src="/js/core/inject-header.js"></script>
```

#### **index.html**
```diff
- <script src="js/alarm-mini-display.js"></script>
+ <script src="js/alarms/mini-display.js"></script>
- <script src="js/alarm-service.js"></script>
+ <script src="js/alarms/service.js"></script>
- <script src="js/cacheManager.js"></script>
+ <script src="js/cacheManager.js"></script> <!-- No change - in public/ -->
- <script type="module" src="js/cross-tab-sync.js"></script>
+ <script type="module" src="js/core/sync.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **instant-test-feedback.html**
```diff
- <script src="js/api-optimization.js"></script>
+ <script src="js/ai/api-optimizer.js"></script>
- <script src="js/api-settings.js"></script>
+ <script src="js/ai/api-settings.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/gemini-api.js"></script>
+ <script src="js/ai/gemini.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/test-feedback.js"></script>
+ <script src="js/academic/test-feedback.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **landing.html**
```diff
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/theme-toggle.js"></script>
+ <script src="js/theme-toggle.js"></script> <!-- File not in list - may need to be created -->
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **priority-calculator.html**
```diff
- <script src="js/common.js"></script>
+ <script src="js/core/common.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
<!-- priority-calculator.js stays at root level -->
```

#### **priority-list.html**
```diff
- <script src="js/common.js"></script>
+ <script src="js/core/common.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/priority-list-sorting.js"></script>
+ <script src="js/tasks/priority-sorting.js"></script>
- <script src="js/priority-list-utils.js"></script>
+ <script src="js/tasks/priority-utils.js"></script>
- <script src="js/priority-sync-fix.js"></script>
+ <script src="js/tasks/priority-sync.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **settings.html**
```diff
- <script src="js/app.js"></script>
+ <script src="js/app.js"></script> <!-- File not in list - may need investigation -->
- <script src="js/gemini-api.js"></script>
+ <script src="js/ai/gemini.js"></script>
- <script src="js/quoteManager.js"></script>
+ <script src="js/utils/quotes.js"></script>
- <script src="js/roleModelManager.js"></script>
+ <script src="js/utils/role-models.js"></script>
- <script src="js/soundManager.js"></script>
+ <script src="js/media/sound-manager.js"></script>
- <script src="js/themeManager.js"></script>
+ <script src="js/ui/theme-manager-alt.js"></script>
- <script src="js/todoistIntegration.js"></script>
+ <script src="js/tasks/todoist.js"></script>
- <script src="js/transitionManager.js"></script>
+ <script src="js/core/transitions.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **sleep-saboteurs.html**
```diff
- <script src="js/alarm-service.js"></script>
+ <script src="js/alarms/service.js"></script>
- <script src="js/clock-display.js"></script>
+ <script src="js/calendar/clock.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/sleep-saboteurs-init.js"></script>
+ <script src="js/health/sleep-saboteurs.js"></script>
- <script src="js/theme-manager.js"></script>
+ <script src="js/ui/theme-manager.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **study-spaces.html**
```diff
- <script src="js/apiSettingsManager.js"></script>
+ <script src="js/ai/settings-manager.js"></script>
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/firebaseAuth.js"></script>
+ <script src="js/auth/firebase-auth.js"></script>
- <script src="js/imageAnalyzer.js"></script>
+ <script src="js/ai/image-analyzer.js"></script>
- <script src="js/scheduleManager.js"></script>
+ <script src="js/calendar/schedule.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/studySpaceAnalyzer.js"></script>
+ <script src="js/study/space-analyzer.js"></script>
- <script src="js/studySpacesManager.js"></script>
+ <script src="js/study/spaces-manager.js"></script>
- <script src="js/timetableAnalyzer.js"></script>
+ <script src="js/calendar/timetable-analyzer.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **subject-marks.html**
```diff
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/data-sync-integration.js"></script>
+ <script src="js/data/sync-integration.js"></script>
- <script src="js/firebase-init.js"></script>
+ <script src="js/auth/init.js"></script>
- <script src="js/firestore-global.js"></script>
+ <script src="js/data/firestore-global.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/subject-marks-integration.js"></script>
+ <script src="js/academic/marks-integration.js"></script>
- <script src="js/subject-marks-ui.js"></script>
+ <script src="js/academic/marks-ui.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **tasks.html**
```diff
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/sideDrawer.js"></script>
+ <script src="js/ui/side-drawer.js"></script>
- <script src="js/taskFilters.js"></script>
+ <script src="js/tasks/filters.js"></script>
- <script src="js/tasksManager.js"></script>
+ <script src="js/tasks/manager.js"></script>
- <script src="js/todoistIntegration.js"></script>
+ <script src="js/tasks/todoist.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **workspace.html**
```diff
- <script src="js/cross-tab-sync.js"></script>
+ <script src="js/core/sync.js"></script>
- <script src="js/sm2.js"></script>
+ <script src="js/study/spaced-repetition.js"></script>
- <script src="js/speech-recognition.js"></script>
+ <script src="js/media/speech-recognition.js"></script>
- <script src="js/speech-synthesis.js"></script>
+ <script src="js/media/speech-synthesis.js"></script>
- <script src="js/workspace-attachments.js"></script>
+ <script src="js/workspace/attachments.js"></script>
- <script src="js/workspace-core.js"></script>
+ <script src="js/workspace/core.js"></script>
- <script src="js/workspace-document.js"></script>
+ <script src="js/workspace/document.js"></script>
- <script src="js/workspaceFlashcardIntegration.js"></script>
+ <script src="js/study/workspace-flashcards.js"></script>
- <script src="js/workspace-formatting.js"></script>
+ <script src="js/workspace/formatting.js"></script>
- <script src="js/workspace-media.js"></script>
+ <script src="js/workspace/media.js"></script>
- <script src="js/workspace-tables-links.js"></script>
+ <script src="js/workspace/tables-links.js"></script>
- <script src="js/workspace-ui.js"></script>
+ <script src="js/workspace/ui.js"></script>
- <script src="/js/inject-header.js"></script>
+ <script src="/js/core/inject-header.js"></script>
```

#### **relaxed-mode/index.html**
```diff
- <script src="../js/common.js"></script>
+ <script src="../js/core/common.js"></script>
- <script src="../js/cross-tab-sync.js"></script>
+ <script src="../js/core/sync.js"></script>
- <script src="../js/sideDrawer.js"></script>
+ <script src="../js/ui/side-drawer.js"></script>
- <script src="../js/tasksManager.js"></script>
+ <script src="../js/tasks/manager.js"></script>
- <script src="../js/transitionManager.js"></script>
+ <script src="../js/core/transitions.js"></script>
```

#### **Youtube Searcher (Not Completed)/index.html**
```diff
- <script src="../js/cross-tab-sync.js"></script>
+ <script src="../js/core/sync.js"></script>
```

## Automated Migration Script

Create a PowerShell script to automate the file moves and HTML updates:

```powershell
# js-migration.ps1
# Phase 1: Create directory structure
$directories = @(
    "js/core", "js/auth", "js/data", "js/tasks", "js/calendar",
    "js/academic", "js/study", "js/workspace", "js/ai", "js/media",
    "js/alarms", "js/health", "js/ui", "js/utils"
)

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path $dir -Force
}

# Phase 2: Move files (preserving git history)
$moves = @{
    "js/common.js" = "js/core/common.js"
    "js/cross-tab-sync.js" = "js/core/sync.js"
    "js/auth.js" = "js/auth/auth.js"
    "js/tasksManager.js" = "js/tasks/manager.js"
    # ... add all other moves
}

foreach ($move in $moves.GetEnumerator()) {
    git mv $move.Key $move.Value
}

# Phase 3: Update HTML files
$htmlUpdates = @{
    "js/cross-tab-sync.js" = "js/core/sync.js"
    "js/sideDrawer.js" = "js/ui/side-drawer.js"
    # ... add all other updates
}

Get-ChildItem -Path "." -Filter "*.html" -Recurse | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    foreach ($update in $htmlUpdates.GetEnumerator()) {
        $content = $content -replace [regex]::Escape($update.Key), $update.Value
    }
    Set-Content -Path $_.FullName -Value $content
}
```

## Summary

This restructuring plan provides:

1. **Clear categorization** of 108 JavaScript files into 10 logical modules
2. **Detailed mapping** from current paths to new paths
3. **Complete HTML update requirements** for all 19 HTML files
4. **Automated migration strategy** with PowerShell scripts
5. **Preservation of git history** through proper file moves

The new structure will significantly improve code organization, maintainability, and developer experience while maintaining all existing functionality.

---
*This restructuring plan provides a foundation for a more maintainable and scalable JavaScript architecture.*
