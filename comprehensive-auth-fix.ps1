# Comprehensive authentication fix for ALL HTML files
Write-Host "COMPREHENSIVE AUTHENTICATION FIX" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# All files that have side-drawer based on our analysis
$sideDrawerFiles = @(
    "grind.html",
    "tasks.html", 
    "priority-list.html",
    "flashcards.html",
    "study-spaces.html",
    "subject-marks.html",
    "academic-details.html",
    "daily-calendar.html",
    "sleep-saboteurs.html",
    "instant-test-feedback.html",
    "priority-calculator.html",
    "extracted.html"
)

# Standard auth setup that works reliably
$standardAuthSetup = @'
    <!-- Standardized Authentication Setup for Side Drawer -->
    <script type="module">
        // Import auth functions from the main auth module
        import { auth, signInWithGoogle, signOutUser } from './js/auth/auth.js';
        
        // Make auth functions globally available for side-drawer
        window.auth = auth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;
        
        // Log successful loading
        console.log('✅ Auth functions loaded for side-drawer:', {
            auth: !!window.auth,
            signInWithGoogle: !!window.signInWithGoogle,
            signOutUser: !!window.signOutUser
        });
    </script>
'@

Write-Host "Processing files with side-drawer..." -ForegroundColor Yellow

foreach ($file in $sideDrawerFiles) {
    if (Test-Path $file) {
        Write-Host ""
        Write-Host "📄 Processing $file..." -ForegroundColor Cyan
        
        $content = Get-Content $file -Raw
        $originalContent = $content
        
        # Check current auth setup
        $hasWindowSignIn = $content -match 'window\.signInWithGoogle'
        $hasWindowSignOut = $content -match 'window\.signOutUser'
        $hasAuthInit = $content -match 'js/auth/init\.js'
        $hasAuthAuth = $content -match 'js/auth/auth\.js'
        $hasInlineAuth = $content -match 'signInWithPopup.*GoogleAuthProvider'
        $hasSideDrawer = $content -match 'side-drawer\.js'
        
        if (-not $hasSideDrawer) {
            Write-Host "  ⚠️  No side-drawer found, skipping..." -ForegroundColor Yellow
            continue
        }
        
        Write-Host "  🔍 Current auth setup detected:" -ForegroundColor Blue
        if ($hasAuthInit) { Write-Host "    - Uses js/auth/init.js" -ForegroundColor White }
        if ($hasAuthAuth) { Write-Host "    - Uses js/auth/auth.js" -ForegroundColor White }
        if ($hasInlineAuth) { Write-Host "    - Uses inline Firebase auth" -ForegroundColor White }
        if ($hasWindowSignIn) { Write-Host "    - Has window.signInWithGoogle" -ForegroundColor Green }
        if ($hasWindowSignOut) { Write-Host "    - Has window.signOutUser" -ForegroundColor Green }
        
        # Determine if we need to fix this file
        $needsFix = $false
        
        if (-not ($hasWindowSignIn -and $hasWindowSignOut)) {
            $needsFix = $true
            Write-Host "  🔧 NEEDS FIX: Missing window auth functions" -ForegroundColor Red
        } elseif ($hasAuthInit -and -not ($hasWindowSignIn -and $hasWindowSignOut)) {
            $needsFix = $true
            Write-Host "  🔧 NEEDS FIX: Using auth/init.js but functions not detected" -ForegroundColor Red
        } else {
            Write-Host "  ✅ OK: Auth setup looks good" -ForegroundColor Green
        }
        
        if ($needsFix) {
            Write-Host "  🛠️  Applying standardized auth fix..." -ForegroundColor Yellow
            
            # Remove existing problematic auth setups
            # Remove js/auth/init.js imports
            $content = $content -replace '<script type="module" src="js/auth/init\.js"></script>', ''
            
            # Remove existing inline auth setups (but preserve other inline scripts)
            $content = $content -replace '(?s)<script type="module">\s*import.*?firebase.*?auth.*?</script>', ''
            
            # Insert standardized auth setup before side-drawer script
            $pattern = '(\s*<script[^>]*src="[^"]*side-drawer\.js"[^>]*></script>)'
            $replacement = $standardAuthSetup + "`r`n" + '$1'
            $content = $content -replace $pattern, $replacement
            
            # Save the updated file
            Set-Content -Path $file -Value $content -Encoding UTF8
            Write-Host "  ✅ Updated $file with standardized auth setup" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ File not found: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 COMPREHENSIVE AUTH FIX COMPLETED!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 WHAT WAS DONE:" -ForegroundColor Yellow
Write-Host "- Analyzed all HTML files with side-drawer" -ForegroundColor White
Write-Host "- Applied standardized auth setup using js/auth/auth.js" -ForegroundColor White
Write-Host "- Ensured proper loading order (auth before side-drawer)" -ForegroundColor White
Write-Host "- Removed problematic js/auth/init.js timing issues" -ForegroundColor White
Write-Host ""
Write-Host "🧪 TESTING:" -ForegroundColor Cyan
Write-Host "1. Clear browser cache completely" -ForegroundColor White
Write-Host "2. Open grind.html (the problematic file)" -ForegroundColor White
Write-Host "3. Open browser console (F12)" -ForegroundColor White
Write-Host "4. Look for '✅ Auth functions loaded' message" -ForegroundColor White
Write-Host "5. Click settings gear to open side drawer" -ForegroundColor White
Write-Host "6. Test Sign In button" -ForegroundColor White
