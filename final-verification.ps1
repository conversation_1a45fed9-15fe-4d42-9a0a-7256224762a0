# Final verification of authentication fixes
Write-Host "FINAL AUTHENTICATION VERIFICATION" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

$testFiles = @("grind.html", "tasks.html", "priority-list.html", "flashcards.html", "study-spaces.html")

foreach ($file in $testFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        $hasSideDrawer = $content -match 'side-drawer\.js'
        $hasSignIn = $content -match 'window\.signInWithGoogle'
        $hasSignOut = $content -match 'window\.signOutUser'
        
        if ($hasSideDrawer) {
            $status = if ($hasSignIn -and $hasSignOut) { "OK" } else { "ISSUE" }
            $color = if ($hasSignIn -and $hasSignOut) { "Green" } else { "Red" }
            Write-Host "$file : $status" -ForegroundColor $color
        }
    }
}

Write-Host ""
Write-Host "Verification complete!" -ForegroundColor Green
Write-Host "All files should now have proper authentication setup." -ForegroundColor Yellow
