# Test script to verify authentication fixes
Write-Host "Testing authentication function availability..." -ForegroundColor Green

$testFiles = @(
    "grind.html",
    "tasks.html",
    "study-spaces.html",
    "subject-marks.html",
    "academic-details.html",
    "daily-calendar.html"
)

foreach ($file in $testFiles) {
    if (Test-Path $file) {
        Write-Host "Checking $file..." -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        
        $hasSideDrawer = $content -match 'side-drawer\.js'
        $hasSignIn = $content -match 'window\.signInWithGoogle'
        $hasSignOut = $content -match 'window\.signOutUser'
        $hasAuthInit = $content -match 'js/auth/init\.js'
        $hasAuthAuth = $content -match 'js/auth/auth\.js'
        
        Write-Host "  Side Drawer: $hasSideDrawer" -ForegroundColor $(if($hasSideDrawer){"Green"}else{"Red"})
        Write-Host "  SignIn Function: $hasSignIn" -ForegroundColor $(if($hasSignIn){"Green"}else{"Red"})
        Write-Host "  SignOut Function: $hasSignOut" -ForegroundColor $(if($hasSignOut){"Green"}else{"Red"})
        Write-Host "  Auth Init: $hasAuthInit" -ForegroundColor $(if($hasAuthInit){"Blue"}else{"Gray"})
        Write-Host "  Auth Auth: $hasAuthAuth" -ForegroundColor $(if($hasAuthAuth){"Blue"}else{"Gray"})
        
        if ($hasSideDrawer -and -not ($hasSignIn -or $hasAuthInit -or $hasAuthAuth)) {
            Write-Host "  ⚠️  WARNING: Has side-drawer but no auth setup!" -ForegroundColor Red
        } elseif ($hasSideDrawer -and ($hasSignIn -or $hasAuthInit -or $hasAuthAuth)) {
            Write-Host "  ✅ OK: Has side-drawer and auth setup" -ForegroundColor Green
        }
        
        Write-Host ""
    }
}

Write-Host "Test completed!" -ForegroundColor Green
