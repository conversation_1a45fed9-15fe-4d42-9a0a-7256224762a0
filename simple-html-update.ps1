# Simple HTML Reference Update
Write-Host "Updating HTML References..." -ForegroundColor Green

# Get all HTML files in root directory
$htmlFiles = Get-ChildItem -Path "." -Filter "*.html" | Where-Object { $_.Name -notlike ".*" }

Write-Host "Found $($htmlFiles.Count) HTML files to update" -ForegroundColor Yellow

# JavaScript reference updates
$jsUpdates = @{
    'js/auth/firebase-config\.js' = 'js/auth/config.js'
    'js/ui/theme-manager\.js' = 'js/ui/theme.js'
    'js/ui/theme-manager-alt\.js' = 'js/ui/theme.js'
    'js/ui/theme-script\.js' = 'js/ui/theme.js'
}

# CSS reference updates
$cssUpdates = @{
    'css/study/spaces-alt\.css' = 'css/study/spaces.css'
    'css/layout/task-display\.css' = 'css/tasks/general.css'
}

Write-Host "Updating JavaScript references..." -ForegroundColor Yellow

foreach ($file in $htmlFiles) {
    $content = Get-Content $file.FullName -Raw
    $changesMade = 0
    
    foreach ($update in $jsUpdates.GetEnumerator()) {
        $oldPath = $update.Key
        $newPath = $update.Value
        
        if ($content -match $oldPath) {
            $content = $content -replace $oldPath, $newPath
            $changesMade++
            Write-Host "  Updated $($file.Name): $oldPath -> $newPath" -ForegroundColor Green
        }
    }
    
    if ($changesMade -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Saved $($file.Name) with $changesMade JS changes" -ForegroundColor Green
    }
}

Write-Host "Updating CSS references..." -ForegroundColor Yellow

foreach ($file in $htmlFiles) {
    $content = Get-Content $file.FullName -Raw
    $changesMade = 0
    
    foreach ($update in $cssUpdates.GetEnumerator()) {
        $oldPath = $update.Key
        $newPath = $update.Value
        
        if ($content -match $oldPath -and $oldPath -ne $newPath) {
            $content = $content -replace $oldPath, $newPath
            $changesMade++
            Write-Host "  Updated $($file.Name): $oldPath -> $newPath" -ForegroundColor Green
        }
    }
    
    if ($changesMade -gt 0) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        Write-Host "  Saved $($file.Name) with $changesMade CSS changes" -ForegroundColor Green
    }
}

Write-Host "Checking for broken references..." -ForegroundColor Yellow

$brokenReferences = @()

foreach ($file in $htmlFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check for JS references
    $jsMatches = [regex]::Matches($content, 'src="([^"]*\.js)"')
    foreach ($match in $jsMatches) {
        $jsPath = $match.Groups[1].Value
        if ($jsPath -match '^(js/|\.\.\/js/)' -and -not $jsPath.StartsWith('http')) {
            $fullPath = $jsPath -replace '^\.\.\/', ''
            if (-not (Test-Path $fullPath)) {
                $brokenReferences += "$($file.Name): $jsPath"
            }
        }
    }
    
    # Check for CSS references
    $cssMatches = [regex]::Matches($content, 'href="([^"]*\.css)"')
    foreach ($match in $cssMatches) {
        $cssPath = $match.Groups[1].Value
        if ($cssPath -match '^(css/|\.\.\/css/)' -and -not $cssPath.StartsWith('http')) {
            $fullPath = $cssPath -replace '^\.\.\/', ''
            if (-not (Test-Path $fullPath)) {
                $brokenReferences += "$($file.Name): $cssPath"
            }
        }
    }
}

if ($brokenReferences.Count -gt 0) {
    Write-Host "Found potential broken references:" -ForegroundColor Red
    foreach ($ref in $brokenReferences) {
        Write-Host "  - $ref" -ForegroundColor Yellow
    }
} else {
    Write-Host "No broken references found!" -ForegroundColor Green
}

Write-Host "HTML reference updates completed!" -ForegroundColor Green
