#!/bin/bash

# CSS Files Restructuring Script
# This script moves files according to the CSS restructuring plan

echo "Starting CSS files restructuring..."

# Core Styles (css/core/)
echo "Moving Core Styles files..."
[ -f "grind.css" ] && mv grind.css css/core/theme.css && echo "✓ grind.css → css/core/theme.css"
[ -f "css/extracted.css" ] && mv css/extracted.css css/core/shared.css && echo "✓ css/extracted.css → css/core/shared.css"
[ -f "styles/main.css" ] && mv styles/main.css css/core/main.css && echo "✓ styles/main.css → css/core/main.css"
[ -f "styles/index.css" ] && mv styles/index.css css/core/index.css && echo "✓ styles/index.css → css/core/index.css"
[ -f "css/compact-style.css" ] && mv css/compact-style.css css/core/compact.css && echo "✓ css/compact-style.css → css/core/compact.css"
[ -f "css/notification.css" ] && mv css/notification.css css/core/notifications.css && echo "✓ css/notification.css → css/core/notifications.css"

# Navigation & Layout (css/layout/)
echo "Moving Navigation & Layout files..."
[ -f "css/sideDrawer.css" ] && mv css/sideDrawer.css css/layout/side-drawer.css && echo "✓ css/sideDrawer.css → css/layout/side-drawer.css"
[ -f "css/task-display.css" ] && mv css/task-display.css css/layout/task-display.css && echo "✓ css/task-display.css → css/layout/task-display.css"

# Academic Features (css/academic/)
echo "Moving Academic Features files..."
[ -f "css/academic-details.css" ] && mv css/academic-details.css css/academic/details.css && echo "✓ css/academic-details.css → css/academic/details.css"
[ -f "css/subject-marks.css" ] && mv css/subject-marks.css css/academic/marks.css && echo "✓ css/subject-marks.css → css/academic/marks.css"
[ -f "css/test-feedback.css" ] && mv css/test-feedback.css css/academic/test-feedback.css && echo "✓ css/test-feedback.css → css/academic/test-feedback.css"

# Task Management (css/tasks/)
echo "Moving Task Management files..."
[ -f "css/task-notes.css" ] && mv css/task-notes.css css/tasks/notes.css && echo "✓ css/task-notes.css → css/tasks/notes.css"
[ -f "css/taskLinks.css" ] && mv css/taskLinks.css css/tasks/links.css && echo "✓ css/taskLinks.css → css/tasks/links.css"
[ -f "css/priority-list.css" ] && mv css/priority-list.css css/tasks/priority-list.css && echo "✓ css/priority-list.css → css/tasks/priority-list.css"
[ -f "css/priority-calculator.css" ] && mv css/priority-calculator.css css/tasks/priority-calculator.css && echo "✓ css/priority-calculator.css → css/tasks/priority-calculator.css"
[ -f "styles/tasks.css" ] && mv styles/tasks.css css/tasks/general.css && echo "✓ styles/tasks.css → css/tasks/general.css"

# Calendar & Scheduling (css/calendar/)
echo "Moving Calendar & Scheduling files..."
[ -f "css/daily-calendar.css" ] && mv css/daily-calendar.css css/calendar/daily.css && echo "✓ css/daily-calendar.css → css/calendar/daily.css"
[ -f "styles/calendar.css" ] && mv styles/calendar.css css/calendar/general.css && echo "✓ styles/calendar.css → css/calendar/general.css"

# Study Tools (css/study/)
echo "Moving Study Tools files..."
[ -f "css/flashcards.css" ] && mv css/flashcards.css css/study/flashcards.css && echo "✓ css/flashcards.css → css/study/flashcards.css"
[ -f "css/study-spaces.css" ] && mv css/study-spaces.css css/study/spaces.css && echo "✓ css/study-spaces.css → css/study/spaces.css"
[ -f "styles/study-spaces.css" ] && mv styles/study-spaces.css css/study/spaces-alt.css && echo "✓ styles/study-spaces.css → css/study/spaces-alt.css"

# Workspace & Documents (css/workspace/)
echo "Moving Workspace & Documents files..."
[ -f "css/workspace.css" ] && mv css/workspace.css css/workspace/editor.css && echo "✓ css/workspace.css → css/workspace/editor.css"
[ -f "css/text-expansion.css" ] && mv css/text-expansion.css css/workspace/text-expansion.css && echo "✓ css/text-expansion.css → css/workspace/text-expansion.css"

# AI & Advanced Features (css/ai/)
echo "Moving AI & Advanced Features files..."
[ -f "css/ai-search-response.css" ] && mv css/ai-search-response.css css/ai/search-response.css && echo "✓ css/ai-search-response.css → css/ai/search-response.css"
[ -f "css/simulation-enhancer.css" ] && mv css/simulation-enhancer.css css/ai/simulation.css && echo "✓ css/simulation-enhancer.css → css/ai/simulation.css"

# Alarms & Notifications (css/alarms/)
echo "Moving Alarms & Notifications files..."
[ -f "css/alarm-service.css" ] && mv css/alarm-service.css css/alarms/service.css && echo "✓ css/alarm-service.css → css/alarms/service.css"

# Health & Wellness (css/health/)
echo "Moving Health & Wellness files..."
[ -f "css/sleep-saboteurs.css" ] && mv css/sleep-saboteurs.css css/health/sleep-tracking.css && echo "✓ css/sleep-saboteurs.css → css/health/sleep-tracking.css"

# Settings & Configuration (css/settings/)
echo "Moving Settings & Configuration files..."
[ -f "css/settings.css" ] && mv css/settings.css css/settings/general.css && echo "✓ css/settings.css → css/settings/general.css"

echo ""
echo "CSS files restructuring completed!"
echo "Checking remaining files in css/ and styles/ directories..."
echo "Files remaining in css/:"
ls css/*.css 2>/dev/null | wc -l | xargs echo "CSS files remaining:"
echo "Files remaining in styles/:"
ls styles/*.css 2>/dev/null | wc -l | xargs echo "Styles files remaining:"

echo ""
echo "Feature modules (preserved):"
echo "relaxed-mode/style.css - $([ -f 'relaxed-mode/style.css' ] && echo 'EXISTS' || echo 'MISSING')"
echo "Youtube Searcher (Not Completed)/styles.css - $([ -f 'Youtube Searcher (Not Completed)/styles.css' ] && echo 'EXISTS' || echo 'MISSING')"
