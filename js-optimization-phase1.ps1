# JavaScript Optimization Phase 1: High Impact Combinations
Write-Host "🚀 JavaScript Optimization Phase 1: High Impact Combinations" -ForegroundColor Green
Write-Host "=============================================================" -ForegroundColor Green

# Phase 1.1: Auth Module Consolidation
Write-Host ""
Write-Host "📁 Phase 1.1: Auth Module Consolidation" -ForegroundColor Yellow
Write-Host "----------------------------------------" -ForegroundColor Yellow

# Backup original files
Write-Host "Creating backups..." -ForegroundColor Cyan
if (-not (Test-Path "backups")) { New-Item -ItemType Directory -Path "backups" -Force }
if (-not (Test-Path "backups/js-auth")) { New-Item -ItemType Directory -Path "backups/js-auth" -Force }

Copy-Item "js/auth/*" "backups/js-auth/" -Recurse -Force
Write-Host "✅ Auth files backed up to backups/js-auth/" -ForegroundColor Green

# Combine firebase-config.js and config.js into unified config
Write-Host "Combining config files..." -ForegroundColor Cyan
$configContent = @'
// Unified Firebase Configuration Module
// Centralizes all Firebase configuration to prevent duplicate initialization

export const firebaseConfig = {
    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
    authDomain: "mzm-gpace.firebaseapp.com",
    projectId: "mzm-gpace",
    storageBucket: "mzm-gpace.firebasestorage.app",
    messagingSenderId: "949014366726",
    appId: "1:949014366726:web:3aa05a6e133e2066c45187"
};

// Legacy Firebase configuration for compatibility
export const legacyFirebaseConfig = {
    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
    authDomain: "mzm-gpace.firebaseapp.com",
    databaseURL: "https://mzm-gpace-default-rtdb.asia-southeast1.firebasedatabase.app",
    projectId: "mzm-gpace",
    storageBucket: "mzm-gpace.appspot.com",
    messagingSenderId: "949014366726",
    appId: "1:949014366726:web:3aa05a6e133e2066c45187",
    measurementId: "G-5KDN1WTCQ1"
};

// Safe Firebase app initialization helper
export function getOrCreateFirebaseApp(initializeApp) {
    try {
        // For Firebase SDK v9 (module-based)
        if (typeof initializeApp === 'function') {
            try {
                return initializeApp(firebaseConfig);
            } catch (e) {
                // If app already exists, Firebase throws 'app/duplicate-app' error
                if (e.code === 'app/duplicate-app') {
                    console.log("Firebase app already exists, using existing app.");
                    return initializeApp();
                }
                throw e;
            }
        } 
        // For Firebase SDK v8 (global namespace)
        else if (window.firebase) {
            if (!firebase.apps.length) {
                return firebase.initializeApp(firebaseConfig);
            }
            return firebase.apps[0];
        }
        else {
            console.error("No Firebase initialization method available");
            return null;
        }
    } catch (error) {
        console.error("Error initializing Firebase app:", error);
        return null;
    }
}
'@

Set-Content -Path "js/auth/config.js" -Value $configContent -Encoding UTF8
Write-Host "✅ Created unified js/auth/config.js" -ForegroundColor Green

# Remove redundant files
Remove-Item "js/auth/firebase-config.js" -Force
Write-Host "✅ Removed redundant js/auth/firebase-config.js" -ForegroundColor Green

# Phase 1.2: UI Theme Management Consolidation
Write-Host ""
Write-Host "📁 Phase 1.2: UI Theme Management Consolidation" -ForegroundColor Yellow
Write-Host "-----------------------------------------------" -ForegroundColor Yellow

# Backup UI files
if (-not (Test-Path "backups/js-ui")) { New-Item -ItemType Directory -Path "backups/js-ui" -Force }
Copy-Item "js/ui/*" "backups/js-ui/" -Recurse -Force
Write-Host "✅ UI files backed up to backups/js-ui/" -ForegroundColor Green

# Create unified theme management
Write-Host "Creating unified theme management..." -ForegroundColor Cyan
$themeContent = @'
/**
 * Unified Theme Management System
 * Combines theme-manager.js, theme-manager-alt.js, and theme-script.js
 */

class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'dark';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupEventListeners();
        this.initializeThemeToggle();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        document.body.className = theme === 'light' ? 'light-theme' : '';
        localStorage.setItem('theme', theme);
        this.currentTheme = theme;
        
        // Update theme toggle buttons
        this.updateThemeButtons();
        
        // Dispatch theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }

    updateThemeButtons() {
        const lightBtns = document.querySelectorAll('.theme-btn[data-theme="light"], .light-theme');
        const darkBtns = document.querySelectorAll('.theme-btn[data-theme="dark"], .dark-theme');
        
        lightBtns.forEach(btn => {
            btn.classList.toggle('active', this.currentTheme === 'light');
        });
        
        darkBtns.forEach(btn => {
            btn.classList.toggle('active', this.currentTheme === 'dark');
        });
    }

    setupEventListeners() {
        // Theme toggle buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.theme-btn[data-theme]')) {
                const theme = e.target.getAttribute('data-theme');
                this.applyTheme(theme);
            }
            
            if (e.target.matches('.theme-toggle, .theme-switch')) {
                this.toggleTheme();
            }
        });
    }

    initializeThemeToggle() {
        // Initialize any theme toggle switches
        const toggles = document.querySelectorAll('.theme-toggle-switch');
        toggles.forEach(toggle => {
            toggle.checked = this.currentTheme === 'light';
            toggle.addEventListener('change', () => this.toggleTheme());
        });
    }

    // Static method for easy access
    static getInstance() {
        if (!window.themeManager) {
            window.themeManager = new ThemeManager();
        }
        return window.themeManager;
    }
}

// Initialize theme manager
const themeManager = ThemeManager.getInstance();

// Export for module usage
export default ThemeManager;

// Global access
window.ThemeManager = ThemeManager;
window.themeManager = themeManager;
'@

Set-Content -Path "js/ui/theme.js" -Value $themeContent -Encoding UTF8
Write-Host "✅ Created unified js/ui/theme.js" -ForegroundColor Green

# Remove redundant theme files
Remove-Item "js/ui/theme-manager.js" -Force -ErrorAction SilentlyContinue
Remove-Item "js/ui/theme-manager-alt.js" -Force -ErrorAction SilentlyContinue
Remove-Item "js/ui/theme-script.js" -Force -ErrorAction SilentlyContinue
Write-Host "✅ Removed redundant theme files" -ForegroundColor Green

# Phase 1.3: Core Utilities Consolidation
Write-Host ""
Write-Host "📁 Phase 1.3: Core Utilities Consolidation" -ForegroundColor Yellow
Write-Host "------------------------------------------" -ForegroundColor Yellow

# Backup core files
if (-not (Test-Path "backups/js-core")) { New-Item -ItemType Directory -Path "backups/js-core" -Force }
Copy-Item "js/core/*" "backups/js-core/" -Recurse -Force
Write-Host "✅ Core files backed up to backups/js-core/" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 Phase 1 Optimization Completed!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host "✅ Auth module consolidated (5 → 3 files)" -ForegroundColor White
Write-Host "✅ UI theme management unified (3 → 1 file)" -ForegroundColor White
Write-Host "✅ All original files backed up" -ForegroundColor White
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Test the consolidated auth and theme functionality" -ForegroundColor White
Write-Host "2. Update HTML file references to new unified files" -ForegroundColor White
Write-Host "3. Run Phase 2 for large file breakdown" -ForegroundColor White
