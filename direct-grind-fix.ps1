# Direct fix for grind.html authentication issue
Write-Host "Fixing grind.html authentication..." -ForegroundColor Green

if (Test-Path "grind.html") {
    $content = Get-Content "grind.html" -Raw
    
    # Check current state
    $hasSideDrawer = $content -match 'side-drawer\.js'
    $hasAuthInit = $content -match 'js/auth/init\.js'
    
    Write-Host "Side drawer found: $hasSideDrawer" -ForegroundColor Yellow
    Write-Host "Auth init found: $hasAuthInit" -ForegroundColor Yellow
    
    if ($hasSideDrawer -and $hasAuthInit) {
        Write-Host "Applying fix..." -ForegroundColor Cyan
        
        # Remove the problematic js/auth/init.js line
        $content = $content -replace '<script type="module" src="js/auth/init\.js"></script>', ''
        
        # Add standardized auth setup before side-drawer
        $authSetup = @'
    <!-- Standardized Authentication Setup -->
    <script type="module">
        import { auth, signInWithGoogle, signOutUser } from './js/auth/auth.js';
        window.auth = auth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;
        console.log('Auth functions loaded:', !!window.signInWithGoogle, !!window.signOutUser);
    </script>
'@
        
        $pattern = '(\s*<script[^>]*src="[^"]*side-drawer\.js"[^>]*></script>)'
        $replacement = $authSetup + "`n" + '$1'
        $content = $content -replace $pattern, $replacement
        
        # Save the file
        Set-Content -Path "grind.html" -Value $content -Encoding UTF8
        Write-Host "grind.html updated successfully!" -ForegroundColor Green
    } else {
        Write-Host "No fix needed or unexpected structure" -ForegroundColor Blue
    }
} else {
    Write-Host "grind.html not found!" -ForegroundColor Red
}

Write-Host "Done!" -ForegroundColor Green
