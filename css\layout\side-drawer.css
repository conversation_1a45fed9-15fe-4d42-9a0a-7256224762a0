/* Refactored Side Drawer CSS based on modern best practices */

/* Rule 1: Use max-width */
/* Rule 2: Use rem for scalable typography & sizing */
/* Rule 3: Use gap instead of margins in flex/grid */
/* Rule 4: Use translate instead of negative margins */
/* Rule 5: Use Logical Properties */
/* Rule 6: Use :has() for parent-based styling */
/* Rule 7: Use clamp() for responsive typography */
/* Rule 8: Use flex: 1 instead of fixed widths in Flexbox */
/* Rule 9: Use aspect-ratio */
/* Rule 10: Use color-mix() for dynamic theming */

/* Assuming :root variables from previous context are available */
/* :root {
    --primary-color: #fe2c55;
    --card-bg: #1e1e1e;
    --hover-bg: #2d2d2d;
    --text-color: #ffffff;
    --border-thin: 0.0625rem;
    --border-medium: 0.125rem;
} */

/* Main side drawer container - slides in from right */
.side-drawer {
    position: fixed;
    /* Rule 5 (logical), Rule 2 (rem) */
    inset-block: 0; /* inset-block-start: 0; inset-block-end: 0; */
    inset-inline-start: auto;
    inset-inline-end: -18.75rem; /* -300px */
    /* Rule 1 (max-width), Rule 2 (rem) */
    max-width: 18.75rem; /* 300px */
    width: 90%; /* Allow shrinking slightly on very narrow screens */
    height: 100vh;
    background: var(--card-bg);
    /* Rule 2 (rem) - Note: Negative horizontal offset might not be ideal with logical props */
    box-shadow: -0.125rem 0 0.3125rem rgba(0, 0, 0, 0.2); /* -2px 0 5px */
    /* Rule 5 (logical) */
    transition: inset-inline-end 0.3s ease;
    z-index: 1000;
    /* Apply gradient border to left side only */
    /* Rule 5 (logical), Rule 2 (rem) */
    border-inline-start-width: 0.1875rem; /* 3px */
    border-style: solid;
    border-image: linear-gradient(to bottom, var(--primary-color), transparent) 1;
}

/* Class added when drawer is opened */
.side-drawer.open {
    /* Rule 5 (logical), Rule 2 (rem) */
    inset-inline-end: 0;
}

/* Container for drawer content with flex column layout */
.drawer-content {
    /* Rule 2 (rem) */
    padding: 1.25rem; /* 20px */
    height: 100%;
    display: flex;
    flex-direction: column;
    /* Improve scrollbar handling */
    overflow-y: auto;
    /* Rule 3 (gap), Rule 2 (rem) */
    gap: 1.5rem; /* 24px */
}

/* Custom scrollbar styling (optional) */
.drawer-content::-webkit-scrollbar {
    width: 0.5rem; /* Rule 2 */
}
.drawer-content::-webkit-scrollbar-track {
    background: transparent;
}
.drawer-content::-webkit-scrollbar-thumb {
    background: var(--hover-bg);
    border-radius: 0.25rem; /* Rule 2 */
}
.drawer-content::-webkit-scrollbar-thumb:hover {
    background: color-mix(in srgb, var(--hover-bg) 70%, var(--text-color) 30%); /* Rule 10 */
}


/* Close button styling */
.drawer-close {
    position: absolute;
    /* Rule 5 (logical), Rule 2 (rem) */
    inset-block-start: 0.625rem; /* 10px */
    inset-inline-end: 0.625rem; /* 10px */
    background: none;
    border: none;
    color: var(--text-color);
    /* Rule 2 (rem) */
    font-size: 1.5rem; /* 24px */
    cursor: pointer;
    /* Rule 2 (rem) */
    padding: 0.3125rem 0.625rem; /* 5px 10px */
    border-radius: 0.25rem; /* 4px */
    line-height: 1; /* Ensure consistent height */
    transition: background-color 0.2s ease; /* Added transition */
}

/* Hover effect for close button */
.drawer-close:hover {
    background: var(--hover-bg);
}

/* Header section styling */
.drawer-header {
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-block-end: 1.875rem; /* 30px */
    padding-block-start: 0.625rem; /* 10px */
}

/* Header text styling */
.drawer-header h3 {
    margin: 0;
    color: var(--text-color);
    /* Rule 7 (clamp), Rule 2 (rem) */
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    /* Add dynamic pseudo-element with attr() */
    position: relative;
}
.drawer-header h3::after {
    content: attr(data-subtitle);
    display: block;
    /* Rule 2 (rem) - Using em for relative sizing */
    font-size: 0.8em;
    opacity: 0.7;
    font-weight: normal;
    margin-block-start: 0.25rem; /* Rule 5, Rule 2 */
}

/* Theme selection section container */
.theme-section {
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-block-end: 1.875rem; /* 30px */
    padding: 0.9375rem; /* 15px */
    background: var(--hover-bg);
    border-radius: 0.5rem; /* 8px */
    /* Better shadow for section */
    /* Rule 2 (rem) */
    filter: drop-shadow(0 0.125rem 0.25rem rgba(0, 0, 0, 0.1)); /* 0 2px 4px */
}

/* Theme section heading */
.theme-section h4 {
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-block-start: 0;
    margin-inline: 0;
    margin-block-end: 0.9375rem; /* 15px */
    color: var(--text-color);
    font-size: 1.1rem; /* Rule 2 */
}

/* Grid container for theme buttons */
.theme-buttons {
    display: grid;
    /* Rule 2 (rem) */
    grid-template-columns: repeat(auto-fit, minmax(7.5rem, 1fr)); /* 120px */
    /* Rule 3 (gap), Rule 2 (rem) */
    gap: 0.625rem; /* 10px */
}

/* Base theme button styling */
.theme-btn {
    /* Rule 2 (rem) */
    padding: 0.625rem; /* 10px */
    border: none;
    border-radius: 0.375rem; /* 6px */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Rule 3 (gap), Rule 2 (rem) */
    gap: 0.5rem; /* 8px */
    transition: all 0.2s ease;
    font-size: 0.9rem; /* Rule 2 */
}

/* Light theme button specific styles */
.theme-btn.light-theme {
    /* Rule 10 (color-mix) */
    background: color-mix(in srgb, #f8f9fa 90%, #000);
    color: #212529;
}

/* Dark theme button specific styles */
.theme-btn.dark-theme {
    /* Rule 10 (color-mix) */
    background: color-mix(in srgb, #212529 90%, #fff);
    color: #f8f9fa;
}

/* Hover effect for theme buttons */
.theme-btn:hover {
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.125rem); /* -2px */
    filter: brightness(1.1); /* Add subtle brightness */
}

/* Active state for selected theme */
.theme-btn.active {
    /* Rule 2 (rem) */
    box-shadow: 0 0 0 var(--border-medium) var(--primary-color); /* 2px */
    outline: var(--border-thin) solid transparent; /* Prevent overlap issues */
}

/* Navigation links container */
.drawer-links {
    display: flex;
    flex-direction: column;
    /* Rule 3 (gap), Rule 2 (rem) */
    gap: 0.625rem; /* 10px */
}

/* User profile section (hidden by default) */
.user-profile {
    /* Rule 2 (rem) */
    padding: 0.9375rem; /* 15px */
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-block-end: 1.25rem; /* 20px */
    background: var(--hover-bg);
    border-radius: 0.5rem; /* 8px */
    display: none; /* JS toggles this */
}
.user-profile.active { /* Add .active class when shown */
    display: block;
}

/* Authentication button styling */
.auth-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Rule 3 (gap), Rule 2 (rem) */
    gap: 0.5rem; /* 8px */
    /* Rule 2 (rem) */
    padding: 0.75rem; /* 12px */
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-block-end: 1.25rem; /* 20px */
    border: none;
    border-radius: 0.5rem; /* 8px */
    background: var(--primary-color);
    color: white;
    cursor: pointer;
    /* Rule 2 (rem) */
    font-size: 1rem; /* 16px */
    transition: all 0.3s ease;
    /* Add animated conic gradient border */
    position: relative;
    z-index: 1;
    overflow: hidden; /* Contain pseudo-element */
}

.auth-button::before {
    content: '';
    position: absolute;
    /* Rule 5 (logical), Rule 2 (rem) */
    inset: -0.1875rem; /* -3px */
    z-index: -1;
    /* Rule 2 (rem) */
    border-radius: 0.625rem; /* 10px (slightly larger than button's 8px) */
    background: conic-gradient(
        from var(--rotation), /* Animate 'from' */
        var(--secondary-color, #25f4ee), /* Use secondary color */
        var(--primary-color),
        var(--secondary-color, #25f4ee)
    );
    --rotation: 0deg;
    /* Rule 4 (translate) - Rotation is handled by animation */
    animation: rotate-border 3s linear infinite;
}

@keyframes rotate-border {
    to { --rotation: 360deg; }
}

/* Hover effect for auth button */
.auth-button:hover {
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.125rem); /* -2px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2); /* 4px 8px */
}

/* User avatar in auth button */
.auth-button .user-avatar {
    /* Rule 2 (rem) */
    width: 1.5rem; /* 24px */
    /* Rule 9 (aspect-ratio) */
    aspect-ratio: 1 / 1;
    /* Use clip-path for unique image shape */
    clip-path: circle(50%);
    object-fit: cover;
    background-color: var(--hover-bg); /* Added fallback background */
}

/* Username text in auth button with ellipsis */
.auth-button .user-name {
    /* Rule 1 (max-width), Rule 2 (rem) */
    max-width: 9.375rem; /* 150px */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1; /* Allow text to take available space */
    text-align: start; /* Rule 5 */
}

/* Logout button in side drawer */
.auth-button .logout-btn {
    /* Rule 2 (rem) */
    padding: 0.375rem 0.75rem; /* 6px 12px */
    border-radius: 0.25rem; /* 4px */
    background: rgba(255, 255, 255, 0.15); /* Slightly transparent white */
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    /* Rule 2 (rem) */
    font-size: 0.875rem; /* 14px */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    /* Glass effect */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-inline-start: auto; /* Push to right side */
}

.auth-button .logout-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    /* Rule 4 (translate), Rule 2 (rem) */
    transform: translateY(-0.0625rem); /* -1px */
    /* Rule 2 (rem) */
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1); /* 2px 4px */
}

/* Navigation link styling */
.drawer-link {
    display: flex;
    align-items: center;
    /* Rule 3 (gap), Rule 2 (rem) */
    gap: 0.625rem; /* 10px */
    /* Rule 2 (rem) */
    padding: 0.75rem; /* 12px */
    color: var(--text-color);
    text-decoration: none;
    border-radius: 0.375rem; /* 6px */
    transition: background 0.2s ease, color 0.2s ease; /* Added color transition */
    font-size: 0.95rem; /* Rule 2 */
}

/* Hover effect for navigation links */
.drawer-link:hover {
    background: var(--hover-bg);
    color: var(--primary-color);
}
/* Active state for drawer link (if needed) */
.drawer-link.active {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
}


/* Drawer toggle button styling (assuming this is outside the drawer) */
.drawer-toggle {
    background: none;
    border: none;
    color: var(--text-color); /* Should inherit from context or be set */
    /* Rule 2 (rem) */
    font-size: 1.25rem; /* 20px */
    cursor: pointer;
    /* Rule 2 (rem) */
    padding: 0.3125rem 0.625rem; /* 5px 10px */
    border-radius: 0.3125rem; /* 5px */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

/* Hover effect for drawer toggle */
.drawer-toggle:hover {
    background-color: var(--hover-bg); /* Use appropriate hover color */
}

/* Icon alignment in drawer toggle */
.drawer-toggle i {
    line-height: 1;
    /* Better shadow for icon */
    /* Rule 2 (rem) */
    filter: drop-shadow(0 0.0625rem 0.125rem rgba(0, 0, 0, 0.2)); /* 0 1px 2px */
}

/* Collapsible accordion section */
.accordion-section {
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-block-end: 0.9375rem; /* 15px */
}

.accordion-section details {
    display: grid;
    /* Animate grid rows */
    grid-template-rows: auto 0fr;
    transition: grid-template-rows 0.3s ease;
    background: var(--hover-bg); /* Added background */
    border-radius: 0.375rem; /* Added radius */
    overflow: hidden; /* Contain content */
}

.accordion-section details[open] {
    grid-template-rows: auto 1fr;
}

.accordion-section summary {
    /* Rule 2 (rem) */
    padding: 0.75rem; /* 12px */
    /* background: var(--hover-bg); */ /* Moved background to details */
    /* border-radius: 0.375rem; */ /* Moved radius to details */
    cursor: pointer;
    font-weight: bold;
    list-style: none; /* Remove default marker */
    display: flex; /* Added for icon alignment */
    justify-content: space-between; /* Added */
    align-items: center; /* Added */
}

/* Remove default markers */
.accordion-section summary::marker,
.accordion-section summary::-webkit-details-marker {
    display: none;
}

/* Add custom marker */
.accordion-section summary::after {
    content: '+';
    transition: transform 0.3s ease;
    font-size: 1.2em; /* Rule 2 */
    font-weight: normal;
}
.accordion-section details[open] summary::after {
    transform: rotate(45deg);
}


.accordion-section .content {
    overflow: hidden;
    /* Rule 2 (rem), Rule 5 (logical) */
    padding: 0.75rem; /* 12px */
    padding-block-start: 0; /* Remove top padding */
    font-size: 0.9rem; /* Rule 2 */
    color: var(--text-secondary); /* Use secondary text color */
}

/* Form elements styling with accent colors */
.drawer-content input[type="checkbox"],
.drawer-content input[type="radio"],
.drawer-content progress {
    accent-color: var(--primary-color);
    cursor: pointer; /* Added */
}

.drawer-content input[type="text"],
.drawer-content input[type="email"],
.drawer-content textarea {
    caret-color: var(--primary-color);
    /* Add basic input styling */
    padding: 0.5rem; /* Rule 2 */
    border-radius: 0.25rem; /* Rule 2 */
    border: var(--border-thin) solid var(--border-color); /* Rule 2 */
    background-color: var(--background-color); /* Or appropriate input bg */
    color: var(--text-color);
    width: 100%; /* Rule 1 */
    margin-block-start: 0.25rem; /* Rule 5, Rule 2 */
}

/* Masonry layout for image gallery */
.image-gallery {
    /* Rule 2 (rem) */
    columns: 2 7.5rem; /* 120px */
    /* Rule 5 (logical), Rule 2 (rem) */
    column-gap: 0.625rem; /* 10px */
}

.image-gallery img {
    display: block;
    width: 100%;
    /* Rule 5 (logical), Rule 2 (rem) */
    margin-block-end: 0.625rem; /* 10px */
    border-radius: 0.25rem; /* 4px */
    /* Rule 9 (aspect-ratio) */
    aspect-ratio: 16 / 9; /* Or desired ratio */
    object-fit: cover; /* Ensure image covers area */
    background-color: var(--hover-bg); /* Fallback background */
}

/* Parent-based styling using :has() */
.drawer-content:has(.user-profile.active) {
    /* Rule 5 (logical), Rule 2 (rem) */
    /* Example: Adjust padding when user profile is active */
    /* padding-block-start: 0.625rem; */
    /* Example: Add border */
    /* border-block-start: var(--border-thin) solid var(--border-color); */
}

/* Responsive container for any content sections */
.content-section {
    /* Rule 1 (max-width) */
    max-width: 100%; /* Ensures it doesn't overflow parent */
    width: 100%;
}

/* Flexbox items using flex: 1 instead of fixed widths */
.flex-container {
    display: flex;
    /* Rule 3 (gap), Rule 2 (rem) */
    gap: 0.625rem; /* 10px */
}

.flex-container .item {
    /* Rule 8 (flex) */
    flex: 1; /* Allows items to grow/shrink equally */
    /* Add some basic styling for visibility */
    background: var(--hover-bg);
    padding: 1rem; /* Rule 2 */
    border-radius: 0.25rem; /* Rule 2 */
}

/* Accessibility: Add focus styles */
:is(.drawer-close, .theme-btn, .auth-button, .drawer-link, .drawer-toggle, .accordion-section summary):focus-visible {
    outline: var(--border-medium) solid var(--primary-color); /* Rule 2 */
    outline-offset: 0.125rem; /* Rule 2 */
    box-shadow: 0 0 0 0.1875rem rgba(var(--primary-color-rgb, 254, 44, 85), 0.3); /* Rule 2 */
}
/* Remove default outline when :focus-visible is supported */
:is(.drawer-close, .theme-btn, .auth-button, .drawer-link, .drawer-toggle, .accordion-section summary):focus {
   outline: none;
}