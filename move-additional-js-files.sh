#!/bin/bash

# Additional JavaScript Files Reorganization Script
# This script moves the remaining 8 files according to the revised reorganization plan

echo "Starting additional JavaScript files reorganization..."

# Tools (js/tools/)
echo "Moving Tools files..."
[ -f "priority-calculator.js" ] && mv priority-calculator.js js/tools/priority-calculator.js && echo "✓ priority-calculator.js → js/tools/priority-calculator.js"
[ -f "priority-calculator-with-worker.js" ] && mv priority-calculator-with-worker.js js/tools/priority-calculator-worker.js && echo "✓ priority-calculator-with-worker.js → js/tools/priority-calculator-worker.js"

# Core functionality (js/core/)
echo "Moving Core functionality files..."
[ -f "public/js/cacheManager.js" ] && mv public/js/cacheManager.js js/core/cache-manager.js && echo "✓ public/js/cacheManager.js → js/core/cache-manager.js"

# UI functionality (js/ui/)
echo "Moving UI functionality files..."
[ -f "scripts/theme.js" ] && mv scripts/theme.js js/ui/theme-script.js && echo "✓ scripts/theme.js → js/ui/theme-script.js"

# Feature modules (js/features/)
echo "Moving Feature modules..."
[ -f "relaxed-mode/script.js" ] && mv relaxed-mode/script.js js/features/relaxed-mode.js && echo "✓ relaxed-mode/script.js → js/features/relaxed-mode.js"
[ -f "Youtube Searcher (Not Completed)/app.js" ] && mv "Youtube Searcher (Not Completed)/app.js" js/features/youtube-searcher.js && echo "✓ Youtube Searcher/app.js → js/features/youtube-searcher.js"

# Workers (js/workers/)
echo "Moving Workers..."
[ -f "workers/imageAnalysis.js" ] && mv workers/imageAnalysis.js js/workers/image-analysis.js && echo "✓ workers/imageAnalysis.js → js/workers/image-analysis.js"
[ -f "test-worker.js" ] && mv test-worker.js js/workers/test-worker.js && echo "✓ test-worker.js → js/workers/test-worker.js"
[ -f "worker.js" ] && mv worker.js js/workers/main-worker.js && echo "✓ worker.js → js/workers/main-worker.js"

echo ""
echo "Additional JavaScript files reorganization completed!"

echo ""
echo "=== VERIFICATION ==="
echo "Files in js/tools/:"
ls js/tools/*.js 2>/dev/null | wc -l | xargs echo "Tools files:"

echo "Files in js/features/:"
ls js/features/*.js 2>/dev/null | wc -l | xargs echo "Feature files:"

echo "Files in js/workers/:"
ls js/workers/*.js 2>/dev/null | wc -l | xargs echo "Worker files:"

echo "Files added to js/core/:"
ls js/core/cache-manager.js 2>/dev/null && echo "✓ cache-manager.js added to core" || echo "✗ cache-manager.js not found"

echo "Files added to js/ui/:"
ls js/ui/theme-script.js 2>/dev/null && echo "✓ theme-script.js added to ui" || echo "✗ theme-script.js not found"

echo ""
echo "=== REMAINING FILES OUTSIDE MODULAR STRUCTURE ==="
echo "Root level JS files (should be minimal):"
ls *.js 2>/dev/null || echo "No root JS files remaining"

echo ""
echo "Public JS files (should only be service-worker.js):"
find public -name "*.js" 2>/dev/null || echo "No public JS files found"

echo ""
echo "Server JS files (backend - should remain):"
find server -name "*.js" 2>/dev/null || echo "No server JS files found"

echo ""
echo "Workers directory (should be empty now):"
ls workers/*.js 2>/dev/null && echo "WARNING: Files still in workers/" || echo "✓ Workers directory cleaned"

echo ""
echo "Scripts directory (should be empty now):"
ls scripts/*.js 2>/dev/null && echo "WARNING: Files still in scripts/" || echo "✓ Scripts directory cleaned"

echo ""
echo "Feature directories (should have no JS files now):"
ls relaxed-mode/*.js 2>/dev/null && echo "WARNING: JS files still in relaxed-mode/" || echo "✓ Relaxed-mode JS cleaned"
ls "Youtube Searcher (Not Completed)"/*.js 2>/dev/null && echo "WARNING: JS files still in YouTube searcher/" || echo "✓ YouTube searcher JS cleaned"
