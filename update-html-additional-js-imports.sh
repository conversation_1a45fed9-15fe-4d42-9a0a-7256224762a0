#!/bin/bash

# HTML Additional JS Import Path Update Script
# This script updates HTML files with new JavaScript import paths for the 8 moved files

echo "Starting HTML additional JS import path updates..."

# Create a mapping of old paths to new paths for the 8 moved files
declare -A additional_js_mappings=(
    # Tools
    ["priority-calculator.js"]="js/tools/priority-calculator.js"
    ["priority-calculator-with-worker.js"]="js/tools/priority-calculator-worker.js"
    
    # Core functionality
    ["js/cacheManager.js"]="js/core/cache-manager.js"
    ["cacheManager.js"]="js/core/cache-manager.js"
    
    # UI functionality
    ["scripts/theme.js"]="js/ui/theme-script.js"
    
    # Workers
    ["worker.js"]="js/workers/main-worker.js"
    ["test-worker.js"]="js/workers/test-worker.js"
    ["workers/imageAnalysis.js"]="js/workers/image-analysis.js"
    
    # Feature modules (relative paths)
    ["script.js"]="../js/features/relaxed-mode.js"
    ["app.js"]="../js/features/youtube-searcher.js"
)

# Function to update a single HTML file
update_html_file() {
    local file="$1"
    local temp_file="${file}.tmp"
    local changes=0
    
    echo "Updating: $file"
    
    # Copy original file to temp
    cp "$file" "$temp_file"
    
    # Apply all additional JS path mappings
    for old_path in "${!additional_js_mappings[@]}"; do
        new_path="${additional_js_mappings[$old_path]}"
        
        # Escape special characters for sed
        old_escaped=$(printf '%s\n' "$old_path" | sed 's/[[\.*^$()+?{|]/\\&/g')
        new_escaped=$(printf '%s\n' "$new_path" | sed 's/[[\.*^$()+?{|]/\\&/g')
        
        # Replace in temp file - handle various script tag patterns
        if sed -i "s|src=[\"']${old_escaped}[\"']|src=\"${new_escaped}\"|g" "$temp_file" 2>/dev/null; then
            # Check if changes were made
            if ! cmp -s "$file" "$temp_file"; then
                changes=$((changes + 1))
            fi
        fi
        
        # Also handle new Worker() calls
        if sed -i "s|new Worker([\"']${old_escaped}[\"'])|new Worker(\"${new_escaped}\")|g" "$temp_file" 2>/dev/null; then
            if ! cmp -s "$file" "$temp_file"; then
                changes=$((changes + 1))
            fi
        fi
        
        # Handle importScripts calls
        if sed -i "s|importScripts([\"']${old_escaped}[\"'])|importScripts(\"${new_escaped}\")|g" "$temp_file" 2>/dev/null; then
            if ! cmp -s "$file" "$temp_file"; then
                changes=$((changes + 1))
            fi
        fi
    done
    
    # If changes were made, replace original with temp
    if [ $changes -gt 0 ]; then
        mv "$temp_file" "$file"
        echo "  ✓ Updated additional JS import paths"
    else
        rm "$temp_file"
        echo "  - No changes needed"
    fi
}

# Update specific HTML files that we know have these imports
echo "Updating specific HTML files with known imports..."

# Update grind.html (has priority-calculator.js)
if [ -f "grind.html" ]; then
    update_html_file "grind.html"
fi

# Update index.html (has cacheManager.js)
if [ -f "index.html" ]; then
    update_html_file "index.html"
fi

# Update priority-calculator.html (has priority-calculator.js)
if [ -f "priority-calculator.html" ]; then
    update_html_file "priority-calculator.html"
fi

# Update relaxed-mode/index.html (has script.js)
if [ -f "relaxed-mode/index.html" ]; then
    echo "Updating: relaxed-mode/index.html"
    sed -i 's|src="script\.js"|src="../js/features/relaxed-mode.js"|g' "relaxed-mode/index.html"
    echo "  ✓ Updated relaxed-mode script path"
fi

# Update Youtube Searcher/index.html (has app.js)
if [ -f "Youtube Searcher (Not Completed)/index.html" ]; then
    echo "Updating: Youtube Searcher (Not Completed)/index.html"
    sed -i 's|src="app\.js"|src="../js/features/youtube-searcher.js"|g' "Youtube Searcher (Not Completed)/index.html"
    echo "  ✓ Updated YouTube searcher script path"
fi

# Search for any remaining references to the old paths
echo ""
echo "=== VERIFICATION - Searching for any remaining old paths ==="

echo "Checking for old priority-calculator.js references:"
grep -r "priority-calculator\.js" *.html 2>/dev/null | grep -v "js/tools/" || echo "✓ No old priority-calculator.js references found"

echo "Checking for old cacheManager.js references:"
grep -r "cacheManager\.js" *.html 2>/dev/null | grep -v "js/core/" || echo "✓ No old cacheManager.js references found"

echo "Checking for old worker.js references:"
grep -r "worker\.js" *.html 2>/dev/null | grep -v "js/workers/" || echo "✓ No old worker.js references found"

echo "Checking for old script.js references in relaxed-mode:"
grep -r "script\.js" "relaxed-mode/" 2>/dev/null | grep -v "js/features/" || echo "✓ No old script.js references found"

echo "Checking for old app.js references in YouTube searcher:"
grep -r "app\.js" "Youtube Searcher (Not Completed)/" 2>/dev/null | grep -v "js/features/" || echo "✓ No old app.js references found"

echo ""
echo "HTML additional JS import path updates completed!"
echo "All moved JavaScript files should now have updated import paths in HTML files."
