# JavaScript Files Tree Structure

This document provides a comprehensive tree structure of all JavaScript files in the codebase.

## Overview
- **Total JS Files**: 108 files
- **Main Directories**: js/, public/, server/, workers/, scripts/, relaxed-mode/
- **Root Level Files**: 6 files

## Complete Tree Structure

```
Creating an App/
├── js/                                    # Main JavaScript directory (87 files)
│   ├── academic-details.js
│   ├── add-favicon.js
│   ├── ai-latex-conversion.js
│   ├── ai-researcher.js
│   ├── alarm-data-service.js
│   ├── alarm-handler.js
│   ├── alarm-mini-display.js
│   ├── alarm-service.js
│   ├── alarm-service-worker.js
│   ├── api-optimization.js
│   ├── api-settings.js
│   ├── apiSettingsManager.js
│   ├── auth.js
│   ├── calendar-views.js
│   ├── calendarManager.js
│   ├── clock-display.js
│   ├── common.js
│   ├── common-header.js
│   ├── cross-tab-sync.js
│   ├── currentTaskManager.js
│   ├── data-loader.js
│   ├── data-sync-integration.js
│   ├── data-sync-manager.js
│   ├── energyHologram.js
│   ├── energyLevels.js
│   ├── fileViewer.js
│   ├── firebase-config.js
│   ├── firebase-init.js
│   ├── firebaseAuth.js
│   ├── firebaseConfig.js
│   ├── firestore.js
│   ├── firestore-global.js
│   ├── flashcardManager.js
│   ├── flashcards.js
│   ├── flashcardTaskIntegration.js
│   ├── gemini-api.js
│   ├── googleDriveApi.js
│   ├── googleGenerativeAI.js
│   ├── grind-speech-synthesis.js
│   ├── imageAnalyzer.js
│   ├── indexedDB.js
│   ├── initFirestoreData.js
│   ├── inject-header.js
│   ├── markdown-converter.js
│   ├── marks-tracking.js
│   ├── pandoc-fallback.js
│   ├── pomodoroGlobal.js
│   ├── pomodoroTimer.js
│   ├── priority-list-sorting.js
│   ├── priority-list-utils.js
│   ├── priority-sync-fix.js
│   ├── priority-worker-wrapper.js
│   ├── quoteManager.js
│   ├── recipeManager.js
│   ├── reorganize-scripts.js
│   ├── roleModelManager.js
│   ├── scheduleManager.js
│   ├── semester-management.js
│   ├── sideDrawer.js
│   ├── simulation/                        # Empty subdirectory
│   ├── simulation-enhancer.js
│   ├── sleep-saboteurs-init.js
│   ├── sleepScheduleManager.js
│   ├── sleepTimeCalculator.js
│   ├── sm2.js
│   ├── soundManager.js
│   ├── speech-recognition.js
│   ├── speech-synthesis.js
│   ├── storageManager.js
│   ├── studySpaceAnalyzer.js
│   ├── studySpacesFirestore.js
│   ├── studySpacesManager.js
│   ├── subject-management.js
│   ├── subject-marks.js
│   ├── subject-marks-integration.js
│   ├── subject-marks-ui.js
│   ├── task-notes.js
│   ├── task-notes-injector.js
│   ├── taskAttachments.js
│   ├── taskFilters.js
│   ├── taskLinks.js
│   ├── tasksManager.js
│   ├── test-feedback.js
│   ├── text-expansion.js
│   ├── theme-manager.js
│   ├── themeManager.js
│   ├── timetableAnalyzer.js
│   ├── timetableIntegration.js
│   ├── todoistIntegration.js
│   ├── transitionManager.js
│   ├── ui-utilities.js
│   ├── update-html-files.js
│   ├── userGuidance.js
│   ├── weightage-connector.js
│   ├── workspace-attachments.js
│   ├── workspace-core.js
│   ├── workspace-document.js
│   ├── workspace-formatting.js
│   ├── workspace-media.js
│   ├── workspace-tables-links.js
│   ├── workspace-ui.js
│   └── workspaceFlashcardIntegration.js
├── public/                                # Public JavaScript files (2 files)
│   ├── js/
│   │   └── cacheManager.js
│   └── service-worker.js
├── server/                                # Server-side JavaScript files (3 files)
│   ├── dataStorage.js
│   ├── routes/
│   │   └── subtasks.js
│   └── timetableHandler.js
├── workers/                               # Web Workers (1 file)
│   └── imageAnalysis.js
├── scripts/                               # Utility scripts (1 file)
│   └── theme.js
├── relaxed-mode/                          # Relaxed mode feature (1 file)
│   └── script.js
├── Youtube Searcher (Not Completed)/      # Incomplete feature (1 file)
│   └── app.js
├── priority-calculator.js                 # Root level files
├── priority-calculator-with-worker.js
├── server.js
├── test-worker.js
└── worker.js
```

## File Categories

### Core Application Files (js/)
- **Authentication & Config**: auth.js, firebase-*.js, firebaseAuth.js, firebaseConfig.js
- **Data Management**: data-*.js, firestore*.js, indexedDB.js, storageManager.js
- **Task Management**: task*.js, currentTaskManager.js, tasksManager.js
- **Calendar & Scheduling**: calendar*.js, schedule*.js, timetable*.js
- **Study Features**: study*.js, flashcard*.js, subject*.js, workspace*.js
- **UI Components**: common*.js, sideDrawer.js, theme*.js, ui-utilities.js
- **API Integration**: api-*.js, gemini-api.js, google*.js, todoist*.js
- **Utilities**: alarm*.js, energy*.js, sleep*.js, speech*.js, sound*.js

### Server Files (server/)
- **Main Server**: server.js (root), dataStorage.js
- **Routes**: subtasks.js
- **Handlers**: timetableHandler.js

### Workers & Scripts
- **Web Workers**: worker.js, test-worker.js, imageAnalysis.js
- **Priority Calculator**: priority-calculator*.js
- **Cache Management**: cacheManager.js
- **Service Worker**: service-worker.js

### Feature Modules
- **Relaxed Mode**: script.js
- **YouTube Searcher**: app.js (incomplete)
- **Theme Management**: theme.js

## Statistics
- **Main JS Directory**: 87 files
- **Server-side**: 4 files (including server.js)
- **Workers**: 3 files
- **Public/Client**: 2 files
- **Feature Modules**: 3 files
- **Root Level**: 6 files
- **Total**: 108 JavaScript files
- **Total Lines of Code**: 44,193 lines

## Largest Files (by lines of code)
1. **ai-researcher.js** - 2,615 lines (AI research functionality)
2. **speech-recognition.js** - 2,081 lines (Speech recognition features)
3. **studySpacesManager.js** - 1,541 lines (Study space management)
4. **flashcards.js** - 1,479 lines (Flashcard system)
5. **googleDriveApi.js** - 1,461 lines (Google Drive integration)
6. **semester-management.js** - 1,449 lines (Semester planning)
7. **test-feedback.js** - 1,441 lines (Test feedback system)
8. **flashcardManager.js** - 1,375 lines (Flashcard management)
9. **calendarManager.js** - 1,101 lines (Calendar functionality)
10. **grind-speech-synthesis.js** - 985 lines (Speech synthesis)

## File Size Distribution
- **Large files (>1000 lines)**: 9 files
- **Medium files (500-1000 lines)**: 12 files
- **Small files (100-500 lines)**: 45 files
- **Tiny files (<100 lines)**: 42 files

## Key Functional Areas
### 🎓 Academic Management
- Semester planning, subject management, marks tracking
- Calendar integration, timetable analysis
- Academic details and progress tracking

### 📚 Study Tools
- Flashcard system with spaced repetition (SM2 algorithm)
- Study spaces management and analysis
- Workspace with document editing capabilities
- Priority calculator for task management

### 🤖 AI Integration
- AI researcher for academic content
- Speech recognition and synthesis
- Google Drive API integration
- Gemini API for AI features

### 📱 User Interface
- Side drawer navigation
- Theme management (light/dark modes)
- Cross-tab synchronization
- Responsive UI utilities

### 🔧 Backend & Data
- Firebase integration (Auth, Firestore)
- IndexedDB for offline storage
- Data synchronization across devices
- API optimization and caching

### ⏰ Productivity Features
- Pomodoro timer with global state
- Alarm system with multiple sounds
- Task management with attachments
- Sleep schedule optimization

---
*Generated on: $(date)*
*Tree structure created using find command and manual organization*
*File statistics generated using wc -l command*
