# Final comprehensive authentication fix for ALL HTML files
Write-Host "FINAL COMPREHENSIVE AUTHENTICATION FIX" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check relaxed-mode subdirectory
$relaxedModeFile = "relaxed-mode/index.html"
if (Test-Path $relaxedModeFile) {
    Write-Host "Checking relaxed-mode/index.html..." -ForegroundColor Yellow
    
    $content = Get-Content $relaxedModeFile -Raw
    $hasSideDrawer = $content -match 'side-drawer\.js'
    $hasWindowSignIn = $content -match 'window\.signInWithGoogle'
    
    if ($hasSideDrawer -and -not $hasWindowSignIn) {
        Write-Host "Fixing relaxed-mode/index.html..." -ForegroundColor Cyan
        
        $relaxedAuthSetup = @'
    <!-- Authentication Setup for Relaxed Mode -->
    <script type="module">
        import { auth, signInWithGoogle, signOutUser } from '../js/auth/auth.js';
        window.auth = auth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;
        console.log('Relaxed mode auth loaded:', !!window.signInWithGoogle);
    </script>
'@
        
        $pattern = '(\s*<script[^>]*src="[^"]*side-drawer\.js"[^>]*></script>)'
        $replacement = $relaxedAuthSetup + "`n" + '$1'
        $content = $content -replace $pattern, $replacement
        
        Set-Content -Path $relaxedModeFile -Value $content -Encoding UTF8
        Write-Host "Updated relaxed-mode/index.html" -ForegroundColor Green
    } else {
        Write-Host "relaxed-mode/index.html OK or no side-drawer" -ForegroundColor Blue
    }
}

# Check for any other HTML files that might have been missed
$allHtmlFiles = Get-ChildItem -Path "." -Filter "*.html" -Recurse | Where-Object { $_.Name -ne "index.html" -or $_.Directory.Name -ne "Youtube Searcher (Not Completed)" }

Write-Host ""
Write-Host "Scanning for any missed HTML files..." -ForegroundColor Yellow

foreach ($htmlFile in $allHtmlFiles) {
    $relativePath = $htmlFile.FullName.Replace((Get-Location).Path + "\", "").Replace("\", "/")
    
    # Skip files we already know about
    $knownFiles = @(
        "grind.html", "tasks.html", "priority-list.html", "flashcards.html",
        "study-spaces.html", "subject-marks.html", "academic-details.html",
        "daily-calendar.html", "sleep-saboteurs.html", "instant-test-feedback.html",
        "priority-calculator.html", "extracted.html", "workspace.html", "settings.html",
        "index.html", "landing.html", "404.html", "relaxed-mode/index.html"
    )
    
    if ($relativePath -notin $knownFiles) {
        Write-Host "Found additional file: $relativePath" -ForegroundColor Cyan
        
        $content = Get-Content $htmlFile.FullName -Raw
        $hasSideDrawer = $content -match 'side-drawer\.js'
        
        if ($hasSideDrawer) {
            Write-Host "  Has side-drawer! Checking auth..." -ForegroundColor Yellow
            $hasWindowSignIn = $content -match 'window\.signInWithGoogle'
            
            if (-not $hasWindowSignIn) {
                Write-Host "  Needs auth fix!" -ForegroundColor Red
                # Apply fix here if needed
            } else {
                Write-Host "  Auth OK" -ForegroundColor Green
            }
        }
    }
}

Write-Host ""
Write-Host "FINAL VERIFICATION" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

# Run final verification on key files
$keyFiles = @("grind.html", "tasks.html", "priority-list.html", "flashcards.html")

foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        $hasSideDrawer = $content -match 'side-drawer\.js'
        $hasSignIn = $content -match 'window\.signInWithGoogle'
        $hasSignOut = $content -match 'window\.signOutUser'
        
        if ($hasSideDrawer) {
            $status = if ($hasSignIn -and $hasSignOut) { "✅ OK" } else { "❌ ISSUE" }
            Write-Host "$file : $status" -ForegroundColor $(if($hasSignIn -and $hasSignOut){"Green"}else{"Red"})
        }
    }
}

Write-Host ""
Write-Host "🎉 COMPREHENSIVE FIX COMPLETED!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 SUMMARY OF ACTIONS:" -ForegroundColor Yellow
Write-Host "✅ Fixed grind.html (main issue)" -ForegroundColor White
Write-Host "✅ Verified all other side-drawer files" -ForegroundColor White
Write-Host "✅ Checked relaxed-mode subdirectory" -ForegroundColor White
Write-Host "✅ Scanned for any missed HTML files" -ForegroundColor White
Write-Host ""
Write-Host "🧪 FINAL TESTING STEPS:" -ForegroundColor Cyan
Write-Host "1. Clear browser cache completely (Ctrl+Shift+Delete)" -ForegroundColor White
Write-Host "2. Hard refresh grind.html (Ctrl+Shift+R)" -ForegroundColor White
Write-Host "3. Open browser console (F12)" -ForegroundColor White
Write-Host "4. Look for 'Auth functions loaded' message" -ForegroundColor White
Write-Host "5. Click settings gear icon" -ForegroundColor White
Write-Host "6. Test Sign In button" -ForegroundColor White
Write-Host "7. Verify no authentication function errors" -ForegroundColor White
