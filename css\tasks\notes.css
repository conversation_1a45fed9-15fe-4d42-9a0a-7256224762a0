/* Task Notes CSS */

/* Notes button in task display - matching the app's modern style */
.notes-btn {
  padding-block: 0.5rem; /* 8px vertical padding */
  padding-inline: 0.9375rem; /* 15px horizontal padding */
  border: none;
  border-radius: 1.25rem; /* 20px pill shape */
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notes-btn:hover {
  transform: translateY(-0.125rem); /* -2px slight lift */
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2); /* 2px 8px shadow */
  background-color: var(--hover-bg, #f0f0f0);
}

.notes-btn i {
  margin-right: 0.3125rem; /* 5px */
}

/* Notes indicator - matching the app's modern style */
.notes-indicator {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--primary-color, #4a6fa5);
  border-radius: 50%;
  margin-left: 0.5rem;
  position: relative;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 111, 165, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(74, 111, 165, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 111, 165, 0);
  }
}

/* Notes modal - matching the app's modern style */
.notes-modal {
  display: none;
  position: fixed;
  inset: 0; /* Cover viewport */
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}

.notes-modal-content {
  background-color: var(--card-bg, #fff);
  border-radius: var(--radius-lg, 12px);
  padding: var(--space-lg, 20px);
  width: 90%;
  max-width: 550px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s ease-out;
  color: var(--text-color, #333); /* Ensure text is visible in both themes */
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notes-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md, 15px);
  border-bottom: 1px solid var(--border-color, #eee);
  padding-bottom: var(--space-sm, 10px);
}

.notes-modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-color, #333);
  font-weight: 600;
  position: relative;
}

/* Add a subtle accent to the header in both themes */
.notes-modal-header h3::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--primary-color, #4a6fa5);
  border-radius: 3px;
}

.notes-modal-close {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--hover-bg, #f0f0f0);
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--text-muted, #777);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.notes-modal-close:hover {
  background: var(--border-color, #ddd);
  color: var(--text-color, #333);
  transform: rotate(90deg);
}

/* Notes form - matching the app's modern style */
.notes-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-md, 15px);
}

.notes-form textarea {
  width: 100%;
  min-height: 120px;
  padding: 0.75rem;
  border: 1px solid var(--border-color, #ddd);
  border-radius: var(--radius-md, 8px);
  resize: vertical;
  font-family: inherit;
  background-color: var(--input-bg, var(--card-bg-light, #f9f9f9));
  color: var(--text-color, #333);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-size: 0.95rem;
}

.notes-form textarea:focus {
  border-color: var(--primary-color, #4a6fa5);
  box-shadow: 0 0 0 2px rgba(74, 111, 165, 0.2);
  outline: none;
}

.notes-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm, 10px);
}

.notes-form-actions button {
  padding-block: 0.625rem; /* 10px */
  padding-inline: 1.25rem; /* 20px */
  border-radius: var(--radius-pill, 20px);
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.notes-cancel-btn {
  background-color: var(--card-bg-light, var(--card-bg, #f5f5f5));
  color: var(--text-color, #333);
  border: 1px solid var(--border-color, #ddd);
}

.notes-cancel-btn:hover {
  background-color: var(--hover-bg, #e8e8e8);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.notes-save-btn {
  background-color: var(--primary-color, #4a6fa5);
  color: white;
}

.notes-save-btn:hover {
  background-color: var(--primary-hover, #3a5a8c);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.4);
}

/* Notes list - matching the app's modern style */
.notes-list {
  margin-top: var(--space-lg, 20px);
  display: flex;
  flex-direction: column;
  gap: var(--space-sm, 10px);
}

.note-item {
  background-color: var(--card-bg-light, var(--hover-bg, #f5f5f5));
  border-radius: var(--radius-md, 8px);
  padding: var(--space-md, 15px);
  position: relative;
  border-left: 3px solid var(--primary-color, #4a6fa5);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  animation: noteAppear 0.3s ease-out;
}

@keyframes noteAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.note-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.note-content {
  margin-bottom: var(--space-sm, 10px);
  white-space: pre-wrap;
  word-break: break-word;
  color: var(--text-color, #333);
  font-size: 0.95rem;
  line-height: 1.5;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: var(--text-muted, #777);
  border-top: 1px solid var(--border-color-light, #eee);
  padding-top: var(--space-xs, 8px);
}

.note-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.note-device {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.note-current-device {
  background-color: rgba(var(--primary-color-rgb, 74, 111, 165), 0.15);
  color: var(--primary-color, #4a6fa5);
}

.note-other-device {
  background-color: rgba(var(--secondary-color-rgb, 46, 204, 113), 0.15);
  color: var(--secondary-color, #2ecc71);
}

body:not(.light-theme) .note-current-device {
  background-color: rgba(var(--primary-color-rgb, 74, 111, 165), 0.25);
  color: var(--primary-color, #5C9CE6);
}

body:not(.light-theme) .note-other-device {
  background-color: rgba(var(--secondary-color-rgb, 46, 204, 113), 0.25);
  color: var(--secondary-color, #3EDB81);
}

.note-actions {
  display: flex;
  gap: var(--space-xs, 8px);
}

.note-edit-btn,
.note-delete-btn {
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  background: var(--card-bg, #fff);
  border: 1px solid var(--border-color-light, var(--border-color, #eee));
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted, #777);
  transition: all 0.2s ease;
}

.note-edit-btn:hover {
  color: var(--primary-color, #4a6fa5);
  background: rgba(74, 111, 165, 0.1);
  transform: scale(1.1);
}

.note-delete-btn:hover {
  color: var(--danger-color, #dc3545);
  background: rgba(220, 53, 69, 0.1);
  transform: scale(1.1);
}

/* Empty state */
.notes-empty {
  text-align: center;
  padding: var(--space-lg, 20px);
  color: var(--text-color, #333);
  font-style: italic;
  background: var(--card-bg-light, var(--hover-bg, #f9f9f9));
  border-radius: var(--radius-md, 8px);
  border: 1px dashed var(--border-color, #ddd);
  font-weight: 500;
}

/* Dark mode specific adjustments */
body:not(.light-theme) .notes-empty {
  background-color: var(--card-bg-light, var(--hover-bg, #2d2d2d));
  color: var(--text-color, #ffffff);
  border-color: var(--border-color, #444);
}

body:not(.light-theme) .notes-form textarea {
  background-color: var(--input-bg, var(--card-bg-light, #2d2d2d));
  border-color: var(--border-color, #444);
}

body:not(.light-theme) .note-item {
  background-color: var(--card-bg-light, var(--hover-bg, #2d2d2d));
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* Highlight notes from current device */
.note-item.note-current-device {
  border-left-color: var(--primary-color, #4a6fa5);
  border-left-width: 4px;
}

body:not(.light-theme) .note-item.note-current-device {
  border-left-color: var(--primary-color, #5C9CE6);
}

body:not(.light-theme) .notes-cancel-btn {
  background-color: var(--card-bg-light, var(--card-bg, #2d2d2d));
  border-color: var(--border-color, #444);
}

body:not(.light-theme) .note-edit-btn,
body:not(.light-theme) .note-delete-btn {
  background-color: var(--card-bg, #1e1e1e);
  border-color: var(--border-color-light, var(--border-color, #444));
  color: var(--text-muted, #aaa);
}

body:not(.light-theme) .notes-modal-close {
  background-color: var(--hover-bg, #2d2d2d);
  color: var(--text-muted, #aaa);
}

body:not(.light-theme) .notes-modal-close:hover {
  background-color: var(--border-color, #444);
  color: var(--text-color, #fff);
}

/* Ensure text is readable in dark mode */
body:not(.light-theme) .note-meta {
  border-top-color: var(--border-color, #444);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notes-modal-content {
    width: 95%;
    max-height: 85vh;
  }
}
