/* Sleep Saboteurs CSS */

:root {
    --primary-color: #fe2c55;
    --secondary-color: #25f4ee;
    --background-color: #121212;
    --text-color: #ffffff;
    --card-bg: #1e1e1e;
    --hover-bg: #2d2d2d;
    --nav-bg: #1a1a1a;
    --border-color: #333;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    min-height: 100vh;
    padding-top: 60px; /* Match extracted.html */
    margin: 0;
    font-family: 'Arial', sans-serif;
}

.top-nav {
    background-color: var(--nav-bg);
    padding: 10px 30px; /* Match extracted.html */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    height: 60px; /* Match extracted.html */
    backdrop-filter: blur(10px); /* Match extracted.html */
}

.nav-brand {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-links a:hover {
    background-color: var(--hover-bg);
}

.nav-links a.active {
    background-color: var(--primary-color);
    color: white;
}

.alarm-container {
    max-width: 600px;
    margin: 0 auto;
    background-color: var(--card-bg);
    border-radius: 25px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.clock {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    background: linear-gradient(145deg, var(--card-bg), var(--background-color));
    padding: 2.5rem;
    border-radius: 25px;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.1),
        inset 0 -2px 6px rgba(255, 255, 255, 0.05);
}

.clock span {
    font-size: 5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    font-family: 'Arial', sans-serif;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    letter-spacing: -2px;
}

.clock .colon {
    font-size: 4.5rem;
    margin: 0 0.8rem;
    color: var(--text-color);
    opacity: 0.7;
    animation: blink 1s infinite;
}

.clock .side {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 1.5rem;
    padding-left: 1.5rem;
    border-left: 3px solid rgba(255, 255, 255, 0.1);
}

.clock .second {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.clock .am-pm {
    font-size: 2rem;
    color: var(--text-color);
    opacity: 0.9;
    font-weight: 500;
}

.days {
    text-align: center;
    margin: 2rem 0;
    background: linear-gradient(145deg, var(--card-bg), var(--background-color));
    padding: 1.5rem;
    border-radius: 20px;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 -2px 6px rgba(255, 255, 255, 0.05);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
}

.days span {
    padding: 0.8rem;
}

.days .month {
    font-size: 2rem;
    color: var(--secondary-color);
    font-weight: 600;
}

.days .day {
    font-size: 2.2rem;
    color: var(--text-color);
    font-weight: 600;
}

.days .date {
    font-size: 2.5rem;
    color: var(--primary-color);
    font-weight: 700;
    min-width: 2.8ch;
    text-align: center;
}

.alarm-form {
    background: linear-gradient(145deg, var(--card-bg), var(--background-color));
    padding: 2rem;
    border-radius: 20px;
    margin: 2rem 0;
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 -2px 6px rgba(255, 255, 255, 0.05);
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.alarm-form.active {
    display: block;
    transform: scale(1);
    opacity: 1;
    pointer-events: auto;
}

.alarm-form input {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    padding: 1rem;
    border-radius: 12px;
    margin-right: 10px;
    width: 100px;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.alarm-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(254, 44, 85, 0.2);
}

.alarm-list {
    margin-top: 2rem;
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--background-color);
}

.alarm-list::-webkit-scrollbar {
    width: 6px;
}

.alarm-list::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: 10px;
}

.alarm-list::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

.alarm-item {
    background: linear-gradient(145deg, var(--card-bg), var(--background-color));
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-radius: 15px;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 -2px 6px rgba(255, 255, 255, 0.05);
    transform: translateY(0);
    transition: all 0.3s ease;
}

.alarm-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.alarm-item .alarm-time {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.alarm-item .alarm-label {
    font-size: 1.1rem;
    color: var(--secondary-color);
    opacity: 0.9;
}

.alarm-button {
    width: 100%;
    padding: 1.2rem;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 15px;
    background: linear-gradient(145deg, var(--primary-color), #ff4d75);
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(254, 44, 85, 0.3);
}

.alarm-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(254, 44, 85, 0.4);
}

.alarm-button i {
    margin-right: 10px;
    font-size: 1.3rem;
}

.alarm-toggle label {
    width: 60px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.alarm-toggle label::before {
    content: '';
    position: absolute;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.alarm-toggle input[type="checkbox"]:checked + label {
    background: var(--primary-color);
}

.alarm-toggle input[type="checkbox"]:checked + label::before {
    transform: translateX(30px);
}

.delete-alarm {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 1.3rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.delete-alarm:hover {
    background: rgba(254, 44, 85, 0.1);
    transform: scale(1.1);
}

.theme-toggle {
    position: fixed;
    top: 80px;
    right: 20px;
    background: transparent;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.theme-text {
    font-size: 14px;
}

.alarm-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--card-bg);
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 10px;
}

.alarm-details {
    flex-grow: 1;
}

.alarm-time {
    font-size: 24px;
    font-weight: bold;
}

.alarm-label {
    color: var(--secondary-color);
    font-size: 14px;
}

.alarm-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.alarm-repeat {
    color: var(--secondary-color);
    font-size: 12px;
}

.alarm-toggle {
    position: relative;
}

.alarm-toggle input[type="checkbox"] {
    display: none;
}

.alarm-toggle label {
    display: inline-block;
    width: 50px;
    height: 25px;
    background-color: var(--border-color);
    border-radius: 25px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s;
}

.alarm-toggle label::before {
    content: '';
    position: absolute;
    width: 21px;
    height: 21px;
    border-radius: 50%;
    background-color: white;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
}

.alarm-toggle input[type="checkbox"]:checked + label {
    background-color: var(--primary-color);
}

.alarm-toggle input[type="checkbox"]:checked + label::before {
    transform: translateX(25px);
}

.alarm-trigger-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.alarm-modal-content {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
}

.alarm-modal-time {
    font-size: 48px;
    margin: 20px 0;
    color: var(--primary-color);
}

.alarm-modal-actions {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.alarm-modal-actions button {
    flex-grow: 1;
    padding: 15px;
    border: none;
    border-radius: 10px;
    background-color: var(--primary-color);
    color: white;
    font-size: 16px;
    cursor: pointer;
}

.alarm-creation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.alarm-creation-content {
    background-color: var(--card-bg);
    padding: 30px;
    border-radius: 20px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 15px 30px rgba(0,0,0,0.4);
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.alarm-creation-content.show {
    transform: scale(1);
    opacity: 1;
}

.alarm-creation-content h2 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: bold;
}

.alarm-input-group {
    margin-bottom: 15px;
}

.alarm-input-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
}

.alarm-input-time {
    width: 100%;
    background-color: var(--background-color);
    border: 2px solid var(--border-color);
    color: var(--text-color);
    padding: 15px;
    border-radius: 12px;
    font-size: 18px;
    transition: all 0.3s ease;
}

.alarm-input-time:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(254, 44, 85, 0.2);
}

.alarm-input-label {
    width: 100%;
    background-color: var(--background-color);
    border: 2px solid var(--border-color);
    color: var(--text-color);
    padding: 15px;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.alarm-input-label:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(254, 44, 85, 0.2);
}

.repeat-days-container {
    margin-bottom: 20px;
}

.repeat-days-label {
    display: block;
    margin-bottom: 10px;
    color: var(--secondary-color);
    font-weight: 600;
}

.repeat-days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
}

.repeat-day-checkbox {
    display: none;
}

.repeat-day-label {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--background-color);
    color: var(--text-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.repeat-day-checkbox:checked + .repeat-day-label {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.alarm-modal-actions {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.alarm-modal-actions button {
    flex-grow: 1;
    padding: 15px;
    border: none;
    border-radius: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.alarm-modal-actions .btn-save {
    background-color: var(--primary-color);
    color: white;
}

.alarm-modal-actions .btn-save:hover {
    background-color: #ff6b81;
}

.alarm-modal-actions .btn-cancel {
    background-color: var(--border-color);
    color: var(--text-color);
}

.alarm-modal-actions .btn-cancel:hover {
    background-color: var(--hover-bg);
}

.alarm-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--background-color);
    border: 2px solid var(--primary-color);
    border-radius: 10px;
    padding: 20px;
    z-index: 1000;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s ease-in-out;
}

.alarm-notification-content {
    text-align: center;
}

.alarm-notification h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.alarm-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

.alarm-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    background: var(--primary-color);
    color: white;
    transition: background-color 0.3s;
}

.alarm-actions button:hover {
    background: #ff4d75;
}

#alarmTone {
    width: calc(100% - 100px);
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--background-color);
    color: var(--text-color);
    margin-right: 10px;
}

.preview-tone {
    padding: 8px 12px;
    background: var(--primary-color);
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s;
}

.preview-tone:hover {
    background: #ff6b81;
}

.alarm-tone-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.alarm-input-group {
    margin-bottom: 15px;
}

.alarm-input-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-color);
}

#newAlarmTime {
    width: calc(100% - 80px);
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--background-color);
    color: var(--text-color);
    margin-right: 10px;
}

#ampm {
    width: 70px;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--background-color);
    color: var(--text-color);
}

.time-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

#enableVibration {
    margin-right: 8px;
}

.snooze-btn, .stop-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.snooze-btn {
    background: var(--secondary-color);
    color: var(--background-color);
}

.stop-btn {
    background: var(--primary-color);
    color: white;
}

.snooze-btn:hover {
    background: #1cd8d2;
}

.stop-btn:hover {
    background: #ff4d75;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Time format toggle */
.time-format-toggle {
    position: absolute;
    top: 100px;
    right: 80px;
    background: var(--card-bg);
    border: none;
    color: var(--text-color);
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.time-format-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.time-format-toggle i {
    color: var(--primary-color);
}

/* Enhanced notification styles */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--card-bg);
    color: var(--text-color);
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    animation: slideInRight 0.3s ease-out;
    z-index: 1000;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Wave animation for alarm trigger */
.wave-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    display: none;
    z-index: 1001;
}

.wave-container.active {
    display: block;
}

.wave {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--primary-color);
    opacity: 0;
    animation: wave 2s infinite;
}

.wave:nth-child(2) {
    animation-delay: 0.5s;
}

.wave:nth-child(3) {
    animation-delay: 1s;
}

@keyframes wave {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}
