# CSS Files Tree Structure

This document provides a comprehensive tree structure of all CSS files in the codebase.

## Overview
- **Total CSS Files**: 30 files
- **Main Directories**: css/, styles/, relaxed-mode/
- **Root Level Files**: 1 file
- **Total Lines of Code**: 20,585 lines

## Complete Tree Structure

```
Creating an App/
├── css/                                   # Main CSS directory (22 files)
│   ├── academic-details.css              # 807 lines - Academic information styling
│   ├── ai-search-response.css            # 599 lines - AI search interface
│   ├── alarm-service.css                 # 557 lines - Alarm system UI
│   ├── compact-style.css                 # 253 lines - Compact view styles
│   ├── daily-calendar.css                # 1,751 lines - Calendar interface
│   ├── extracted.css                     # 1,810 lines - Extracted/shared styles
│   ├── flashcards.css                    # 270 lines - Flashcard system
│   ├── notification.css                  # 53 lines - Notification styles
│   ├── priority-calculator.css           # 419 lines - Priority calculation UI
│   ├── priority-list.css                 # 532 lines - Priority list interface
│   ├── settings.css                      # 379 lines - Settings page
│   ├── sideDrawer.css                    # 538 lines - Navigation drawer
│   ├── simulation-enhancer.css           # 241 lines - Simulation features
│   ├── sleep-saboteurs.css               # 880 lines - Sleep tracking UI
│   ├── study-spaces.css                  # 862 lines - Study space management
│   ├── subject-marks.css                 # 246 lines - Subject grades display
│   ├── task-display.css                  # 38 lines - Task display components
│   ├── task-notes.css                    # 401 lines - Task notes interface
│   ├── taskLinks.css                     # 356 lines - Task linking system
│   ├── test-feedback.css                 # 770 lines - Test feedback UI
│   ├── text-expansion.css                # 264 lines - Text expansion features
│   └── workspace.css                     # 1,440 lines - Workspace editor
├── styles/                               # Alternative styles directory (5 files)
│   ├── calendar.css                      # 533 lines - Calendar styling
│   ├── index.css                         # 21 lines - Main page styles
│   ├── main.css                          # 826 lines - Core application styles
│   ├── study-spaces.css                  # 227 lines - Study spaces (alternative)
│   └── tasks.css                         # 293 lines - Task management styles
├── relaxed-mode/                         # Relaxed mode feature (1 file)
│   └── style.css                         # 502 lines - Relaxed mode styling
├── Youtube Searcher (Not Completed)/     # Incomplete feature (1 file)
│   └── styles.css                        # 140 lines - YouTube searcher UI
└── grind.css                             # 4,577 lines - Main application theme
```

## File Categories

### 🎨 Core Styling
- **grind.css** (4,577 lines) - Main application theme and base styles
- **extracted.css** (1,810 lines) - Shared/extracted common styles
- **main.css** (826 lines) - Core application styles

### 📚 Academic Features
- **academic-details.css** (807 lines) - Academic information display
- **subject-marks.css** (246 lines) - Grade and marks tracking
- **test-feedback.css** (770 lines) - Test result feedback interface
- **flashcards.css** (270 lines) - Flashcard study system

### 📅 Calendar & Scheduling
- **daily-calendar.css** (1,751 lines) - Main calendar interface
- **calendar.css** (533 lines) - Alternative calendar styling

### 📝 Task Management
- **task-notes.css** (401 lines) - Task note-taking interface
- **task-display.css** (38 lines) - Task display components
- **taskLinks.css** (356 lines) - Task linking and relationships
- **tasks.css** (293 lines) - General task management
- **priority-list.css** (532 lines) - Priority-based task lists
- **priority-calculator.css** (419 lines) - Priority calculation tools

### 🏢 Study Environment
- **study-spaces.css** (862 lines) - Study space management
- **workspace.css** (1,440 lines) - Document workspace editor
- **sleep-saboteurs.css** (880 lines) - Sleep tracking and optimization

### 🔧 UI Components
- **sideDrawer.css** (538 lines) - Navigation drawer/sidebar
- **settings.css** (379 lines) - Application settings interface
- **notification.css** (53 lines) - Notification system
- **compact-style.css** (253 lines) - Compact view layouts

### 🤖 AI & Advanced Features
- **ai-search-response.css** (599 lines) - AI search interface
- **simulation-enhancer.css** (241 lines) - Simulation features
- **text-expansion.css** (264 lines) - Text expansion utilities

### ⏰ Time Management
- **alarm-service.css** (557 lines) - Alarm and reminder system

### 🎮 Special Modes
- **relaxed-mode/style.css** (502 lines) - Relaxed study mode
- **Youtube Searcher styles.css** (140 lines) - YouTube integration (incomplete)

## Statistics by Size

### Large Files (>1000 lines)
1. **grind.css** - 4,577 lines (Main theme)
2. **extracted.css** - 1,810 lines (Shared styles)
3. **daily-calendar.css** - 1,751 lines (Calendar)
4. **workspace.css** - 1,440 lines (Editor)

### Medium Files (500-1000 lines)
- sleep-saboteurs.css (880 lines)
- study-spaces.css (862 lines)
- main.css (826 lines)
- academic-details.css (807 lines)
- test-feedback.css (770 lines)
- ai-search-response.css (599 lines)
- alarm-service.css (557 lines)
- sideDrawer.css (538 lines)
- calendar.css (533 lines)
- priority-list.css (532 lines)
- relaxed-mode/style.css (502 lines)

### Small Files (100-500 lines)
- priority-calculator.css (419 lines)
- task-notes.css (401 lines)
- settings.css (379 lines)
- taskLinks.css (356 lines)
- tasks.css (293 lines)
- flashcards.css (270 lines)
- text-expansion.css (264 lines)
- compact-style.css (253 lines)
- subject-marks.css (246 lines)
- simulation-enhancer.css (241 lines)
- study-spaces.css (227 lines)
- Youtube Searcher styles.css (140 lines)

### Tiny Files (<100 lines)
- notification.css (53 lines)
- task-display.css (38 lines)
- index.css (21 lines)

## File Size Distribution
- **Extra Large (>2000 lines)**: 2 files
- **Large (1000-2000 lines)**: 2 files
- **Medium (500-1000 lines)**: 11 files
- **Small (100-500 lines)**: 12 files
- **Tiny (<100 lines)**: 3 files

---
*Generated on: $(date)*
*Tree structure created using find command and manual organization*
*File statistics generated using wc -l command*
