# Simple comprehensive authentication analysis
Write-Host "COMPREHENSIVE AUTHENTICATION ANALYSIS" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Core HTML files that likely need side-drawer
$coreFiles = @(
    "grind.html",
    "tasks.html", 
    "priority-list.html",
    "flashcards.html",
    "study-spaces.html",
    "subject-marks.html",
    "academic-details.html",
    "daily-calendar.html",
    "settings.html",
    "sleep-saboteurs.html",
    "instant-test-feedback.html",
    "priority-calculator.html",
    "extracted.html",
    "workspace.html"
)

$sideDrawerFiles = @()
$authIssues = @()

foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        Write-Host "Checking $file..." -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw
        
        $hasSideDrawer = $content -match 'side-drawer\.js'
        $hasWindowSignIn = $content -match 'window\.signInWithGoogle'
        $hasWindowSignOut = $content -match 'window\.signOutUser'
        $hasAuthInit = $content -match 'js/auth/init\.js'
        $hasAuthAuth = $content -match 'js/auth/auth\.js'
        $hasInlineAuth = $content -match 'signInWithPopup.*GoogleAuthProvider'
        
        if ($hasSideDrawer) {
            $sideDrawerFiles += $file
            Write-Host "  HAS SIDE-DRAWER" -ForegroundColor Green
            
            # Determine auth pattern
            if ($hasAuthInit) {
                Write-Host "  Uses: js/auth/init.js" -ForegroundColor Blue
            } elseif ($hasAuthAuth) {
                Write-Host "  Uses: js/auth/auth.js" -ForegroundColor Blue
            } elseif ($hasInlineAuth) {
                Write-Host "  Uses: Inline Firebase Auth" -ForegroundColor Blue
            } else {
                Write-Host "  Uses: UNKNOWN/MISSING AUTH" -ForegroundColor Red
                $authIssues += "$file - No recognizable auth pattern"
            }
            
            # Check function availability
            if ($hasWindowSignIn) {
                Write-Host "  SignIn: OK" -ForegroundColor Green
            } else {
                Write-Host "  SignIn: MISSING" -ForegroundColor Red
                if ($file -notin $authIssues) {
                    $authIssues += "$file - Missing signIn function"
                }
            }
            
            if ($hasWindowSignOut) {
                Write-Host "  SignOut: OK" -ForegroundColor Green
            } else {
                Write-Host "  SignOut: MISSING" -ForegroundColor Red
                if ($file -notin $authIssues) {
                    $authIssues += "$file - Missing signOut function"
                }
            }
        } else {
            Write-Host "  No side-drawer" -ForegroundColor Gray
        }
        Write-Host ""
    }
}

Write-Host "SUMMARY:" -ForegroundColor Yellow
Write-Host "Files with side-drawer: $($sideDrawerFiles.Count)" -ForegroundColor White
Write-Host "Files with auth issues: $($authIssues.Count)" -ForegroundColor White

if ($authIssues.Count -gt 0) {
    Write-Host ""
    Write-Host "AUTH ISSUES FOUND:" -ForegroundColor Red
    foreach ($issue in $authIssues) {
        Write-Host "  $issue" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "FILES WITH SIDE-DRAWER:" -ForegroundColor Cyan
foreach ($file in $sideDrawerFiles) {
    Write-Host "  $file" -ForegroundColor White
}
