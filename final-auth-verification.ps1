# Final verification and summary of authentication fixes
Write-Host "🔍 Final Authentication Fix Verification" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Test all HTML files that should have side-drawer functionality
$testFiles = @(
    "grind.html",
    "tasks.html", 
    "priority-list.html",
    "flashcards.html",
    "study-spaces.html",
    "subject-marks.html",
    "academic-details.html",
    "daily-calendar.html",
    "sleep-saboteurs.html",
    "instant-test-feedback.html",
    "priority-calculator.html",
    "extracted.html",
    "relaxed-mode/index.html"
)

$allGood = $true
$issuesFound = @()

foreach ($file in $testFiles) {
    if (Test-Path $file) {
        Write-Host "📄 Checking $file..." -ForegroundColor Cyan
        
        $content = Get-Content $file -Raw
        
        $hasSideDrawer = $content -match 'side-drawer\.js'
        $hasSignIn = $content -match 'window\.signInWithGoogle'
        $hasSignOut = $content -match 'window\.signOutUser'
        $hasAuthInit = $content -match 'js/auth/init\.js'
        $hasAuthAuth = $content -match 'js/auth/auth\.js'
        
        if ($hasSideDrawer) {
            $hasAnyAuth = $hasSignIn -or $hasAuthInit -or $hasAuthAuth
            
            if (-not $hasAnyAuth) {
                Write-Host "  ❌ ERROR: Has side-drawer but no auth setup!" -ForegroundColor Red
                $allGood = $false
                $issuesFound += "$file - Missing authentication setup"
            } elseif ($hasSignIn -and -not $hasSignOut) {
                Write-Host "  ⚠️  WARNING: Has signIn but missing signOut!" -ForegroundColor Yellow
                $issuesFound += "$file - Missing signOut function"
            } else {
                Write-Host "  ✅ OK: Properly configured" -ForegroundColor Green
            }
        } else {
            Write-Host "  ℹ️  No side-drawer (OK)" -ForegroundColor Blue
        }
    } else {
        Write-Host "  ❌ File not found: $file" -ForegroundColor Red
        $allGood = $false
        $issuesFound += "$file - File not found"
    }
}
    } else {
        Write-Host "  ❌ File not found: $file" -ForegroundColor Red
        $allGood = $false
        $issuesFound += "$file - File not found"
    }
}

Write-Host ""
Write-Host "📋 SUMMARY" -ForegroundColor Yellow
Write-Host "==========" -ForegroundColor Yellow

if ($allGood -and $issuesFound.Count -eq 0) {
    Write-Host "🎉 All authentication issues have been resolved!" -ForegroundColor Green
    Write-Host "✅ All files with side-drawer have proper auth setup" -ForegroundColor Green
} else {
    Write-Host "⚠️  Some issues were found:" -ForegroundColor Yellow
    foreach ($issue in $issuesFound) {
        Write-Host "  - $issue" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🧪 TESTING INSTRUCTIONS" -ForegroundColor Cyan
Write-Host "=======================" -ForegroundColor Cyan
Write-Host "1. Open any page with side-drawer in your browser" -ForegroundColor White
Write-Host "2. Open browser console (F12)" -ForegroundColor White
Write-Host "3. Look for authentication function loading messages" -ForegroundColor White
Write-Host "4. Click the settings/gear icon to open side drawer" -ForegroundColor White
Write-Host "5. Try the Sign In button" -ForegroundColor White
Write-Host "6. Check console for any 'window.signInWithGoogle is not a function' errors" -ForegroundColor White

Write-Host ""
Write-Host "🔧 If you still see errors:" -ForegroundColor Yellow
Write-Host "- Clear browser cache (Ctrl+Shift+Delete)" -ForegroundColor White
Write-Host "- Hard refresh the page (Ctrl+Shift+R)" -ForegroundColor White
Write-Host "- Check browser console for any import errors" -ForegroundColor White
