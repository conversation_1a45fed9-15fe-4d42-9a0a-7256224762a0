# CSS Files Restructuring Plan

## Overview
This document proposes a comprehensive categorization and modularization plan for the 30 CSS files currently scattered across the codebase. The goal is to create a logical, maintainable, and scalable CSS architecture that mirrors the JavaScript modular structure.

## Current vs Proposed Structure

### Current Issues
- **Mixed directories**: Files scattered across `css/`, `styles/`, and root
- **Inconsistent organization**: No clear categorization by functionality
- **Unclear dependencies**: Related styles scattered throughout
- **Maintenance difficulty**: Hard to locate and organize related styling

### Proposed Benefits
- **Logical grouping**: Related styles organized together
- **Clear dependencies**: Module boundaries and relationships
- **Easier maintenance**: Predictable file locations
- **Scalability**: Room for future growth
- **Better imports**: Cleaner import paths

## Current CSS File Analysis

### **Total Files**: 30 CSS files with 20,585 lines of code

#### **Current Distribution**:
- **css/ directory**: 22 files (main styling directory)
- **styles/ directory**: 5 files (alternative styles)
- **Root level**: 1 file (grind.css - main theme)
- **Feature directories**: 2 files (relaxed-mode, YouTube searcher)

#### **HTML Dependencies Analysis** (from html-files-tree.md):
- **css/sideDrawer.css**: Used in 15/19 HTML files (79% coverage) - UNIVERSAL
- **main.css**: Used in 6/19 HTML files (32% coverage) - CORE
- **grind.css**: Used in 3/19 HTML files (16% coverage) - THEME

## Detailed Restructuring Plan

### 📁 **Core Styles** (`css/core/`)
**Purpose**: Essential system-wide styling and themes

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `grind.css` | `css/core/theme.css` | 4,577 | Main application theme | 3 files |
| `css/extracted.css` | `css/core/shared.css` | 1,810 | Shared/extracted styles | 1 file |
| `styles/main.css` | `css/core/main.css` | 826 | Core application styles | 6 files |
| `styles/index.css` | `css/core/index.css` | 21 | Landing page styles | 0 files |
| `css/compact-style.css` | `css/core/compact.css` | 253 | Compact view layouts | 0 files |
| `css/notification.css` | `css/core/notifications.css` | 53 | Global notifications | 0 files |

### 🧭 **Navigation & Layout** (`css/layout/`)
**Purpose**: Navigation, layout, and structural components

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/sideDrawer.css` | `css/layout/side-drawer.css` | 538 | Navigation drawer | 15 files |
| `css/task-display.css` | `css/layout/task-display.css` | 38 | Task display components | 2 files |

### 🎓 **Academic Features** (`css/academic/`)
**Purpose**: Academic tracking, subjects, and educational interfaces

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/academic-details.css` | `css/academic/details.css` | 807 | Academic information | 1 file |
| `css/subject-marks.css` | `css/academic/marks.css` | 246 | Subject grades display | 1 file |
| `css/test-feedback.css` | `css/academic/test-feedback.css` | 770 | Test feedback interface | 1 file |

### 📝 **Task Management** (`css/tasks/`)
**Purpose**: Task creation, management, and organization

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/task-notes.css` | `css/tasks/notes.css` | 401 | Task notes interface | 1 file |
| `css/taskLinks.css` | `css/tasks/links.css` | 356 | Task linking system | 1 file |
| `css/priority-list.css` | `css/tasks/priority-list.css` | 532 | Priority-based lists | 1 file |
| `css/priority-calculator.css` | `css/tasks/priority-calculator.css` | 419 | Priority calculation | 1 file |
| `styles/tasks.css` | `css/tasks/general.css` | 293 | General task management | 0 files |

### 📅 **Calendar & Scheduling** (`css/calendar/`)
**Purpose**: Calendar, scheduling, and time management

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/daily-calendar.css` | `css/calendar/daily.css` | 1,751 | Daily calendar interface | 1 file |
| `styles/calendar.css` | `css/calendar/general.css` | 533 | General calendar styling | 0 files |

### 📚 **Study Tools** (`css/study/`)
**Purpose**: Study aids, flashcards, and learning tools

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/flashcards.css` | `css/study/flashcards.css` | 270 | Flashcard system | 1 file |
| `css/study-spaces.css` | `css/study/spaces.css` | 862 | Study space management | 1 file |
| `styles/study-spaces.css` | `css/study/spaces-alt.css` | 227 | Alternative study spaces | 0 files |

### 📄 **Workspace & Documents** (`css/workspace/`)
**Purpose**: Document editing, workspace management

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/workspace.css` | `css/workspace/editor.css` | 1,440 | Document workspace | 1 file |
| `css/text-expansion.css` | `css/workspace/text-expansion.css` | 264 | Text expansion features | 1 file |

### 🤖 **AI & Advanced Features** (`css/ai/`)
**Purpose**: AI integration, search, and intelligent features

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/ai-search-response.css` | `css/ai/search-response.css` | 599 | AI search interface | 1 file |
| `css/simulation-enhancer.css` | `css/ai/simulation.css` | 241 | Simulation features | 1 file |

### ⏰ **Alarms & Notifications** (`css/alarms/`)
**Purpose**: Alarm system, notifications, and alerts

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/alarm-service.css` | `css/alarms/service.css` | 557 | Alarm system UI | 2 files |

### 😴 **Health & Wellness** (`css/health/`)
**Purpose**: Sleep tracking, wellness features

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/sleep-saboteurs.css` | `css/health/sleep-tracking.css` | 880 | Sleep tracking UI | 1 file |

### ⚙️ **Settings & Configuration** (`css/settings/`)
**Purpose**: Application settings and configuration

| Current Path | New Path | Lines | Purpose | HTML Usage |
|--------------|----------|-------|---------|------------|
| `css/settings.css` | `css/settings/general.css` | 379 | Settings interface | 1 file |

## Feature Modules (No Change)
These files remain in their feature-specific directories:

| Current Path | New Path | Reason |
|--------------|----------|---------|
| `relaxed-mode/style.css` | `relaxed-mode/style.css` | Self-contained feature |
| `Youtube Searcher (Not Completed)/styles.css` | `Youtube Searcher (Not Completed)/styles.css` | Incomplete feature module |

## Implementation Strategy

### Phase 1: Create Directory Structure
```bash
mkdir -p css/{core,layout,academic,tasks,calendar,study,workspace,ai,alarms,health,settings}
```

### Phase 2: Move Files (with git mv for history preservation)
```bash
# Example moves
git mv grind.css css/core/theme.css
git mv css/sideDrawer.css css/layout/side-drawer.css
git mv css/academic-details.css css/academic/details.css
# ... continue for all files
```

### Phase 3: Update Import Paths
- Update all HTML files with new import paths
- Update any CSS @import statements
- Update build scripts and configurations

### Phase 4: Create Index Files
Create main CSS files in each directory to import related styles:
```css
/* css/academic/index.css */
@import './details.css';
@import './marks.css';
@import './test-feedback.css';
```

## Benefits of This Structure

### 🎯 **Improved Organization**
- **Logical grouping**: Related styles are together
- **Clear boundaries**: Each module has a specific purpose
- **Predictable locations**: Easy to find styles

### 🔧 **Better Maintainability**
- **Easier debugging**: Related styles are co-located
- **Simpler refactoring**: Clear module boundaries
- **Reduced conflicts**: Less chance of naming collisions

### 📈 **Enhanced Scalability**
- **Room for growth**: Each category can expand independently
- **Modular architecture**: Easy to add new features
- **Clear dependencies**: Better understanding of relationships

### 👥 **Developer Experience**
- **Faster navigation**: Intuitive file locations
- **Better IDE support**: Improved autocomplete and navigation
- **Clearer imports**: More descriptive import paths

## HTML Import Path Updates Required

### Files Requiring Path Updates

Based on the dependency analysis from `html-files-tree.md`, the following HTML files will need their CSS import paths updated:

#### **academic-details.html**
```diff
- <link rel="stylesheet" href="css/academic-details.css">
+ <link rel="stylesheet" href="css/academic/details.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
```

#### **daily-calendar.html**
```diff
- <link rel="stylesheet" href="css/daily-calendar.css">
+ <link rel="stylesheet" href="css/calendar/daily.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="css/task-display.css">
+ <link rel="stylesheet" href="css/layout/task-display.css">
- <link rel="stylesheet" href="main.css">
+ <link rel="stylesheet" href="css/core/main.css">
```

#### **extracted.html**
```diff
- <link rel="stylesheet" href="css/extracted.css">
+ <link rel="stylesheet" href="css/core/shared.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
```

#### **flashcards.html**
```diff
- <link rel="stylesheet" href="css/flashcards.css">
+ <link rel="stylesheet" href="css/study/flashcards.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="grind.css">
+ <link rel="stylesheet" href="css/core/theme.css">
- <link rel="stylesheet" href="main.css">
+ <link rel="stylesheet" href="css/core/main.css">
```

#### **grind.html** (Major updates required)
```diff
- <link rel="stylesheet" href="css/ai-search-response.css">
+ <link rel="stylesheet" href="css/ai/search-response.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="css/simulation-enhancer.css">
+ <link rel="stylesheet" href="css/ai/simulation.css">
- <link rel="stylesheet" href="css/task-display.css">
+ <link rel="stylesheet" href="css/layout/task-display.css">
- <link rel="stylesheet" href="css/taskLinks.css">
+ <link rel="stylesheet" href="css/tasks/links.css">
- <link rel="stylesheet" href="css/task-notes.css">
+ <link rel="stylesheet" href="css/tasks/notes.css">
- <link rel="stylesheet" href="css/text-expansion.css">
+ <link rel="stylesheet" href="css/workspace/text-expansion.css">
- <link rel="stylesheet" href="grind.css">
+ <link rel="stylesheet" href="css/core/theme.css">
- <link rel="stylesheet" href="main.css">
+ <link rel="stylesheet" href="css/core/main.css">
```

#### **index.html**
```diff
- <link rel="stylesheet" href="css/alarm-service.css">
+ <link rel="stylesheet" href="css/alarms/service.css">
```

#### **instant-test-feedback.html**
```diff
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="css/test-feedback.css">
+ <link rel="stylesheet" href="css/academic/test-feedback.css">
- <link rel="stylesheet" href="grind.css">
+ <link rel="stylesheet" href="css/core/theme.css">
- <link rel="stylesheet" href="main.css">
+ <link rel="stylesheet" href="css/core/main.css">
```

#### **landing.html**
```diff
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="styles/main.css">
+ <link rel="stylesheet" href="css/core/main.css">
```

#### **priority-calculator.html**
```diff
- <link rel="stylesheet" href="css/priority-calculator.css">
+ <link rel="stylesheet" href="css/tasks/priority-calculator.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
```

#### **priority-list.html**
```diff
- <link rel="stylesheet" href="css/priority-list.css">
+ <link rel="stylesheet" href="css/tasks/priority-list.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
```

#### **settings.html**
```diff
- <link rel="stylesheet" href="css/settings.css">
+ <link rel="stylesheet" href="css/settings/general.css">
```

#### **sleep-saboteurs.html**
```diff
- <link rel="stylesheet" href="css/alarm-service.css">
+ <link rel="stylesheet" href="css/alarms/service.css">
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="css/sleep-saboteurs.css">
+ <link rel="stylesheet" href="css/health/sleep-tracking.css">
```

#### **study-spaces.html**
```diff
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="css/study-spaces.css">
+ <link rel="stylesheet" href="css/study/spaces.css">
- <link rel="stylesheet" href="main.css">
+ <link rel="stylesheet" href="css/core/main.css">
```

#### **subject-marks.html**
```diff
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
- <link rel="stylesheet" href="css/subject-marks.css">
+ <link rel="stylesheet" href="css/academic/marks.css">
```

#### **tasks.html**
```diff
- <link rel="stylesheet" href="css/sideDrawer.css">
+ <link rel="stylesheet" href="css/layout/side-drawer.css">
```

#### **workspace.html**
```diff
- <link rel="stylesheet" href="css/workspace.css">
+ <link rel="stylesheet" href="css/workspace/editor.css">
```

#### **relaxed-mode/index.html**
```diff
- <link rel="stylesheet" href="../css/sideDrawer.css">
+ <link rel="stylesheet" href="../css/layout/side-drawer.css">
- <link rel="stylesheet" href="../styles/main.css">
+ <link rel="stylesheet" href="../css/core/main.css">
<!-- style.css remains unchanged -->
```

#### **Youtube Searcher (Not Completed)/index.html**
```diff
<!-- styles.css remains unchanged as it's a feature module -->
```

## Automated Migration Script

Create a PowerShell script to automate the file moves and HTML updates:

```powershell
# css-migration.ps1
# Phase 1: Create directory structure
$directories = @(
    "css/core", "css/layout", "css/academic", "css/tasks",
    "css/calendar", "css/study", "css/workspace", "css/ai",
    "css/alarms", "css/health", "css/settings"
)

foreach ($dir in $directories) {
    New-Item -ItemType Directory -Path $dir -Force
}

# Phase 2: Move files (preserving git history)
$moves = @{
    "grind.css" = "css/core/theme.css"
    "css/extracted.css" = "css/core/shared.css"
    "styles/main.css" = "css/core/main.css"
    "css/sideDrawer.css" = "css/layout/side-drawer.css"
    "css/academic-details.css" = "css/academic/details.css"
    # ... add all other moves
}

foreach ($move in $moves.GetEnumerator()) {
    git mv $move.Key $move.Value
}

# Phase 3: Update HTML files
$cssUpdates = @{
    "css/sideDrawer.css" = "css/layout/side-drawer.css"
    "grind.css" = "css/core/theme.css"
    "styles/main.css" = "css/core/main.css"
    # ... add all other updates
}

Get-ChildItem -Path "." -Filter "*.html" -Recurse | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    foreach ($update in $cssUpdates.GetEnumerator()) {
        $content = $content -replace [regex]::Escape($update.Key), $update.Value
    }
    Set-Content -Path $_.FullName -Value $content
}
```

## Summary

This restructuring plan provides:

1. **Clear categorization** of 30 CSS files into 10 logical modules
2. **Detailed mapping** from current paths to new paths
3. **Complete HTML update requirements** for all affected files
4. **Automated migration strategy** with PowerShell scripts
5. **Preservation of git history** through proper file moves

The new structure will significantly improve CSS organization, maintainability, and developer experience while maintaining all existing functionality.

---
*This restructuring plan provides a foundation for a more maintainable and scalable CSS architecture.*
