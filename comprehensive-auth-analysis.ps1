# Comprehensive authentication analysis for ALL HTML files
Write-Host "🔍 COMPREHENSIVE AUTHENTICATION ANALYSIS" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Get all HTML files
$allHtmlFiles = @(
    "404.html",
    "academic-details.html", 
    "daily-calendar.html",
    "extracted.html",
    "flashcards.html",
    "grind.html",
    "index.html",
    "instant-test-feedback.html",
    "landing.html",
    "priority-calculator.html",
    "priority-list.html",
    "settings.html",
    "sleep-saboteurs.html",
    "study-spaces.html",
    "subject-marks.html",
    "tasks.html",
    "workspace.html",
    "relaxed-mode/index.html",
    "Youtube Searcher (Not Completed)/index.html"
)

$filesWithSideDrawer = @()
$filesWithAuthIssues = @()
$authPatterns = @{}

foreach ($file in $allHtmlFiles) {
    if (Test-Path $file) {
        Write-Host "📄 Analyzing $file..." -ForegroundColor Cyan
        
        $content = Get-Content $file -Raw
        
        # Check for side-drawer
        $hasSideDrawer = $content -match 'side-drawer\.js'
        
        # Check for various auth patterns
        $hasWindowSignIn = $content -match 'window\.signInWithGoogle'
        $hasWindowSignOut = $content -match 'window\.signOutUser'
        $hasAuthInit = $content -match 'js/auth/init\.js'
        $hasAuthAuth = $content -match 'js/auth/auth\.js'
        $hasFirebaseAuth = $content -match 'js/auth/firebase-auth\.js'
        $hasInlineAuth = $content -match 'signInWithPopup.*GoogleAuthProvider'
        $hasFirebaseImport = $content -match 'firebase.*auth'
        
        # Determine auth pattern
        $authPattern = "None"
        if ($hasAuthInit) { $authPattern = "auth/init.js" }
        elseif ($hasAuthAuth) { $authPattern = "auth/auth.js" }
        elseif ($hasFirebaseAuth) { $authPattern = "auth/firebase-auth.js" }
        elseif ($hasInlineAuth) { $authPattern = "Inline Firebase" }
        elseif ($hasFirebaseImport) { $authPattern = "Firebase Import" }
        
        $authPatterns[$file] = $authPattern
        
        Write-Host "  Side Drawer: $hasSideDrawer" -ForegroundColor $(if($hasSideDrawer){"Green"}else{"Gray"})
        Write-Host "  Auth Pattern: $authPattern" -ForegroundColor Blue
        Write-Host "  Window SignIn: $hasWindowSignIn" -ForegroundColor $(if($hasWindowSignIn){"Green"}else{"Red"})
        Write-Host "  Window SignOut: $hasWindowSignOut" -ForegroundColor $(if($hasWindowSignOut){"Green"}else{"Red"})
        
        if ($hasSideDrawer) {
            $filesWithSideDrawer += $file
            
            # Check if auth setup is adequate
            $hasAnyAuth = $hasWindowSignIn -or $hasAuthInit -or $hasAuthAuth -or $hasFirebaseAuth -or $hasInlineAuth
            $hasBothFunctions = $hasWindowSignIn -and $hasWindowSignOut
            
            if (-not $hasAnyAuth) {
                Write-Host "  ❌ CRITICAL: Side drawer but NO auth setup!" -ForegroundColor Red
                $filesWithAuthIssues += "$file - No auth setup"
            } elseif ($hasAnyAuth -and -not $hasBothFunctions -and $authPattern -ne "auth/init.js") {
                Write-Host "  ⚠️  WARNING: Incomplete auth setup!" -ForegroundColor Yellow
                $filesWithAuthIssues += "$file - Incomplete auth ($authPattern)"
            } else {
                Write-Host "  ✅ OK: Auth setup looks good" -ForegroundColor Green
            }
        }
        
        Write-Host ""
    } else {
        Write-Host "❌ File not found: $file" -ForegroundColor Red
    }
}

Write-Host "📊 SUMMARY REPORT" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow
Write-Host "Total HTML files analyzed: $($allHtmlFiles.Count)" -ForegroundColor White
Write-Host "Files with side-drawer: $($filesWithSideDrawer.Count)" -ForegroundColor White
Write-Host "Files with auth issues: $($filesWithAuthIssues.Count)" -ForegroundColor White

Write-Host ""
Write-Host "🎯 FILES WITH SIDE-DRAWER:" -ForegroundColor Cyan
foreach ($file in $filesWithSideDrawer) {
    Write-Host "  - $file ($($authPatterns[$file]))" -ForegroundColor White
}

if ($filesWithAuthIssues.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️  FILES WITH AUTH ISSUES:" -ForegroundColor Red
    foreach ($issue in $filesWithAuthIssues) {
        Write-Host "  - $issue" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "AUTH PATTERNS FOUND:" -ForegroundColor Magenta
foreach ($pattern in $authPatterns.GetEnumerator()) {
    if ($pattern.Value -ne "None") {
        Write-Host "  $($pattern.Key) : $($pattern.Value)" -ForegroundColor White
    }
}
