/* Text Expansion Styles */

/* Modal styles */
#snippetManagerModal.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

#snippetManagerModal .modal-content {
    background-color: var(--card-bg, #1e1e1e);
    color: var(--text-color, #ffffff);
    margin: 5% auto;
    padding: 1.25rem;
    border: 1px solid var(--border-color, #333);
    width: 90%;
    max-width: 800px;
    border-radius: var(--border-radius-lg, 0.75rem);
    box-shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.2);
    max-height: 90vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color, #fe2c55) var(--card-bg, #1e1e1e);
}

#snippetManagerModal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color, #333);
    padding-bottom: 0.75rem;
    margin-bottom: 1.25rem;
}

#snippetManagerModal .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color, #ffffff);
}

#snippetManagerModal .close {
    color: var(--text-muted, #6c757d);
    font-size: 1.75rem;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

#snippetManagerModal .close:hover,
#snippetManagerModal .close:focus {
    color: var(--text-color, #ffffff);
    text-decoration: none;
}

/* Form styles */
#snippetManagerModal .snippet-form {
    margin-bottom: 1.875rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid var(--border-color, #333);
}

#snippetManagerModal .form-group {
    margin-bottom: 1rem;
}

#snippetManagerModal .form-group label {
    display: block;
    margin-bottom: 0.375rem;
    font-weight: 500;
    color: var(--text-color, #ffffff);
}

#snippetManagerModal .form-group input,
#snippetManagerModal .form-group textarea {
    width: 100%;
    padding: 0.625rem;
    background-color: var(--hover-bg, #2d2d2d);
    color: var(--text-color, #ffffff);
    border: 1px solid var(--border-color, #333);
    border-radius: var(--border-radius-sm, 0.25rem);
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

#snippetManagerModal .form-group input:focus,
#snippetManagerModal .form-group textarea:focus {
    border-color: var(--primary-color, #fe2c55);
    box-shadow: 0 0 0 2px rgba(254, 44, 85, 0.2);
    outline: none;
}

#snippetManagerModal .form-group textarea {
    min-height: 5rem;
    resize: vertical;
}

/* Button styles */
#snippetManagerModal .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius-sm, 0.25rem);
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#snippetManagerModal .btn-primary {
    background-color: var(--primary-color, #fe2c55);
    color: white;
}

#snippetManagerModal .btn-primary:hover {
    transform: translateY(-0.125rem);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

#snippetManagerModal .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

#snippetManagerModal .btn-warning {
    background-color: var(--warning-color, #f59e0b);
    color: white;
}

#snippetManagerModal .btn-warning:hover {
    transform: translateY(-0.125rem);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

#snippetManagerModal .btn-edit {
    background-color: var(--secondary-color, #25f4ee);
    color: black;
}

#snippetManagerModal .btn-edit:hover {
    transform: translateY(-0.125rem);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

#snippetManagerModal .btn-delete {
    background-color: var(--error-color, #ef4444);
    color: white;
}

#snippetManagerModal .btn-delete:hover {
    transform: translateY(-0.125rem);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

/* Snippet list styles */
#snippetManagerModal .snippet-list h3 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    color: var(--text-color, #ffffff);
}

#snippetManagerModal .snippet-list .snippet-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 1rem;
}

#snippetManagerModal .list-group {
    margin-top: 1rem;
}

#snippetManagerModal .list-group-item {
    border: 1px solid var(--border-color, #333);
    border-radius: var(--border-radius-sm, 0.25rem);
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background-color: var(--hover-bg, #2d2d2d);
    transition: all 0.3s ease;
}

#snippetManagerModal .list-group-item:hover {
    transform: translateY(-0.125rem);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.2);
}

#snippetManagerModal .snippet-item {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 0.75rem;
}

#snippetManagerModal .snippet-info {
    flex: 1;
    min-width: 150px;
}

#snippetManagerModal .snippet-shortcut {
    font-weight: bold;
    font-size: 1rem;
    color: var(--primary-color, #fe2c55);
}

#snippetManagerModal .snippet-description {
    color: var(--text-muted, #6c757d);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

#snippetManagerModal .snippet-text {
    flex: 2;
    margin: 0 0.75rem;
    padding: 0.5rem;
    background-color: var(--card-bg, #1e1e1e);
    border: 1px solid var(--border-color, #333);
    border-radius: var(--border-radius-sm, 0.25rem);
    font-family: monospace;
    white-space: pre-wrap;
    overflow-wrap: break-word;
    max-height: 6.25rem;
    overflow-y: auto;
    color: var(--text-color, #ffffff);
    font-size: 0.875rem;
}

#snippetManagerModal .snippet-actions {
    display: flex;
    gap: 0.375rem;
}

/* No custom styles needed for the button as it uses the existing tool-btn class */

/* Responsive adjustments */
@media (max-width: 768px) {
    #snippetManagerModal .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 1rem;
    }

    #snippetManagerModal .snippet-item {
        flex-direction: column;
        align-items: flex-start;
    }

    #snippetManagerModal .snippet-text {
        margin: 0.625rem 0;
        width: 100%;
    }

    #snippetManagerModal .snippet-actions {
        margin-top: 0.625rem;
        width: 100%;
        justify-content: flex-end;
    }
}
