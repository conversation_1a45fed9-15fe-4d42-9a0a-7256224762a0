﻿/**
 * Unified Theme Management System
 */
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'dark';
        this.init();
    }

    init() {
        this.applyTheme(this.currentTheme);
        this.setupEventListeners();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        document.body.className = theme === 'light' ? 'light-theme' : '';
        localStorage.setItem('theme', theme);
        this.currentTheme = theme;
        this.updateThemeButtons();
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
    }

    updateThemeButtons() {
        const lightBtns = document.querySelectorAll('.theme-btn[data-theme="light"], .light-theme');
        const darkBtns = document.querySelectorAll('.theme-btn[data-theme="dark"], .dark-theme');
        
        lightBtns.forEach(btn => {
            btn.classList.toggle('active', this.currentTheme === 'light');
        });
        
        darkBtns.forEach(btn => {
            btn.classList.toggle('active', this.currentTheme === 'dark');
        });
    }

    setupEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.theme-btn[data-theme]')) {
                const theme = e.target.getAttribute('data-theme');
                this.applyTheme(theme);
            }
            
            if (e.target.matches('.theme-toggle, .theme-switch')) {
                this.toggleTheme();
            }
        });
    }

    static getInstance() {
        if (!window.themeManager) {
            window.themeManager = new ThemeManager();
        }
        return window.themeManager;
    }
}

const themeManager = ThemeManager.getInstance();
export default ThemeManager;
window.ThemeManager = ThemeManager;
window.themeManager = themeManager;
