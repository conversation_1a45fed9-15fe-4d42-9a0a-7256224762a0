# Final Verification of Optimization
Write-Host "FINAL OPTIMIZATION VERIFICATION" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Count files
$currentJsCount = (Get-ChildItem "js" -Recurse -Filter "*.js").Count
$currentCssCount = (Get-ChildItem "css" -Recurse -Filter "*.css").Count

Write-Host "Current JS files: $currentJsCount" -ForegroundColor White
Write-Host "Current CSS files: $currentCssCount" -ForegroundColor White

# Check for broken references
Write-Host ""
Write-Host "Checking for broken references..." -ForegroundColor Yellow

$htmlFiles = Get-ChildItem -Path "." -Filter "*.html"
$brokenRefs = @()

foreach ($file in $htmlFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # Check JS references
    $jsMatches = [regex]::Matches($content, 'src="([^"]*\.js)"')
    foreach ($match in $jsMatches) {
        $jsPath = $match.Groups[1].Value
        if ($jsPath -match '^js/' -and -not $jsPath.StartsWith('http')) {
            if (-not (Test-Path $jsPath)) {
                $brokenRefs += "$($file.Name): $jsPath"
            }
        }
    }
    
    # Check CSS references
    $cssMatches = [regex]::Matches($content, 'href="([^"]*\.css)"')
    foreach ($match in $cssMatches) {
        $cssPath = $match.Groups[1].Value
        if ($cssPath -match '^css/' -and -not $cssPath.StartsWith('http')) {
            if (-not (Test-Path $cssPath)) {
                $brokenRefs += "$($file.Name): $cssPath"
            }
        }
    }
}

if ($brokenRefs.Count -eq 0) {
    Write-Host "No broken references found!" -ForegroundColor Green
} else {
    Write-Host "Found broken references:" -ForegroundColor Red
    foreach ($ref in $brokenRefs) {
        Write-Host "  - $ref" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "VERIFICATION COMPLETED!" -ForegroundColor Green
Write-Host "Total JS files: $currentJsCount" -ForegroundColor White
Write-Host "Total CSS files: $currentCssCount" -ForegroundColor White
Write-Host "Broken references: $($brokenRefs.Count)" -ForegroundColor White
