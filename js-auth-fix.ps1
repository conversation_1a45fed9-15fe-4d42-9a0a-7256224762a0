# js-auth-fix.ps1 - Fix authentication function exposure after JS restructuring

Write-Host "🔧 Fixing authentication function exposure after JS restructuring..." -ForegroundColor Green

# Step 1: Fix auth modules to properly export functions
Write-Host "📝 Updating auth modules..." -ForegroundColor Yellow

# First, let's check what auth files exist and fix them
$authFiles = @(
    "js/auth/auth.js",
    "js/auth/firebase-auth.js", 
    "js/auth/init.js"
)

foreach ($authFile in $authFiles) {
    if (Test-Path $authFile) {
        Write-Host "  📄 Found: $authFile" -ForegroundColor Cyan
        
        $content = Get-Content $authFile -Raw
        
        # Ensure signOutUser is exported and available globally
        if ($content -notmatch "window\.signOutUser") {
            Write-Host "    ⚠️  Missing window.signOutUser in $authFile" -ForegroundColor Yellow
        }
        
        if ($content -notmatch "window\.signInWithGoogle") {
            Write-Host "    ⚠️  Missing window.signInWithGoogle in $authFile" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ❌ Not found: $authFile" -ForegroundColor Red
    }
}

# Step 2: Standardize HTML authentication setup
Write-Host "🌐 Standardizing HTML authentication setup..." -ForegroundColor Yellow

# Files that use side-drawer and need authentication
$htmlFilesWithSideDrawer = @(
    "grind.html",
    "tasks.html", 
    "priority-list.html",
    "flashcards.html",
    "workspace.html",
    "study-spaces.html",
    "subject-marks.html",
    "academic-details.html",
    "daily-calendar.html",
    "settings.html",
    "sleep-saboteurs.html",
    "instant-test-feedback.html",
    "priority-calculator.html",
    "extracted.html"
)

# Standard authentication setup template
$authSetupTemplate = @"
    <!-- Authentication Setup for Side Drawer -->
    <script type="module">
        // Import auth functions from the restructured auth module
        import { auth, signInWithGoogle, signOutUser } from './js/auth/auth.js';

        // Make auth functions globally available for side-drawer
        window.auth = auth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;

        console.log('Authentication functions loaded for side-drawer:', {
            auth: !!window.auth,
            signInWithGoogle: !!window.signInWithGoogle,
            signOutUser: !!window.signOutUser
        });
    </script>
"@

foreach ($file in $htmlFilesWithSideDrawer) {
    if (Test-Path $file) {
        Write-Host "  📄 Processing $file..." -ForegroundColor Cyan
        
        $content = Get-Content $file -Raw
        
        # Check if file contains side-drawer script
        if ($content -match 'side-drawer\.js') {
            Write-Host "    🎯 Found side-drawer.js reference" -ForegroundColor Green
            
            # Check if it already has proper auth setup
            if ($content -notmatch "window\.signInWithGoogle.*window\.signOutUser") {
                Write-Host "    🔧 Adding standardized auth setup..." -ForegroundColor Yellow

                # Insert standardized auth setup before side-drawer script
                $content = $content -replace '(\s*<script[^>]*src="[^"]*side-drawer\.js"[^>]*></script>)', "$authSetupTemplate`r`n`$1"

                Set-Content -Path $file -Value $content -Encoding UTF8
                Write-Host "    ✅ Updated $file" -ForegroundColor Green
            } else {
                Write-Host "    ℹ️  Auth setup already present in $file" -ForegroundColor Blue
            }
        } else {
            Write-Host "    ⚠️  No side-drawer.js found in $file" -ForegroundColor Yellow
        }
    } else {
        Write-Host "    ❌ File not found: $file" -ForegroundColor Red
    }
}
        } else {
            Write-Host "    ⚠️  No side-drawer.js found in $file" -ForegroundColor Yellow
        }
    } else {
        Write-Host "    ❌ File not found: $file" -ForegroundColor Red
    }
}

# Step 3: Handle relaxed-mode subdirectory
$relaxedModeFile = "relaxed-mode/index.html"
if (Test-Path $relaxedModeFile) {
    Write-Host "  📄 Processing $relaxedModeFile..." -ForegroundColor Cyan
    
    $content = Get-Content $relaxedModeFile -Raw
    
    if ($content -match 'side-drawer\.js') {
        # For relaxed-mode, adjust the import path
        $relaxedAuthSetup = @"
    <!-- Authentication Setup for Side Drawer -->
    <script type="module">
        // Import auth functions from the restructured auth module (parent directory)
        import { auth, signInWithGoogle, signOutUser } from '../js/auth/auth.js';

        // Make auth functions globally available for side-drawer
        window.auth = auth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;

        console.log('Authentication functions loaded for side-drawer:', {
            auth: !!window.auth,
            signInWithGoogle: !!window.signInWithGoogle,
            signOutUser: !!window.signOutUser
        });
    </script>
"@
        
        if ($content -notmatch "window\.signInWithGoogle.*window\.signOutUser") {
            $content = $content -replace '(\s*<script[^>]*src="[^"]*side-drawer\.js"[^>]*></script>)', "$relaxedAuthSetup`r`n`$1"
            Set-Content -Path $relaxedModeFile -Value $content -Encoding UTF8
            Write-Host "    ✅ Updated $relaxedModeFile" -ForegroundColor Green
        }
    }
}

Write-Host ""
Write-Host "🎉 Authentication fixes completed!" -ForegroundColor Green
Write-Host "🔍 Please test the side-drawer authentication in your browser." -ForegroundColor Yellow
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Open any page with side-drawer in browser" -ForegroundColor White
Write-Host "   2. Open browser console (F12)" -ForegroundColor White
Write-Host "   3. Look for 'Authentication functions loaded' message" -ForegroundColor White
Write-Host "   4. Test sign-in functionality in side drawer" -ForegroundColor White
