<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Subject Marks</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/layout/side-drawer.css" rel="stylesheet">
    <link href="css/academic/marks.css" rel="stylesheet">

    <!-- Firebase and Data Modules - Order matters for initialization -->





    <!-- CSS moved to subject-marks.css -->
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand d-flex align-items-center">
            <img
                src="assets/images/gpace-logo-white.png"
                alt="GPAce Logo"
                style="height: 60px; margin-right: 0px;"
            >
            <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
        </div>
        <div class="nav-links">
            <a href="grind.html">Grind Mode</a>
            <a href="study-spaces.html">Grind Station</a>
            <a href="daily-calendar.html">Daily Drip</a>
            <a href="academic-details.html">Brain Juice</a>
            <a href="extracted.html">Hustle Hub</a>
            <a href="subject-marks.html" class="active">Subject Marks</a>
            <a href="flashcards.html">Flashcards</a>
        </div>
    </nav>

    <div class="container">
        <h1 class="mb-4">Subject Marks & Performance</h1>

        <!-- Subject Selection -->
        <div class="card mb-4">
            <h3>Select a Subject</h3>
            <div class="form-group">
                <select id="subjectSelector" class="form-select">
                    <option value="">-- Select a Subject --</option>
                    <!-- Subjects will be populated here -->
                </select>
            </div>
        </div>

        <!-- Subject Performance -->
        <div id="subjectPerformance" class="card mb-4 d-none">
            <h3>Subject Performance</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4 id="subjectName"></h4>
                    <p>Credit Hours: <span id="creditHours"></span></p>
                    <p>Academic Performance: <span id="academicPerformance"></span></p>
                    <div class="progress">
                        <div id="performanceBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h4>Category Contributions</h4>
                    <div id="categoryContributions">
                        <!-- Category contributions will be shown here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Weightages -->
        <div id="categoryWeightages" class="card mb-4 d-none">
            <h3>Category Weightages</h3>
            <p class="text-muted">Set the weightage for each assessment category (total must be 100%)</p>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Assignments (%)</label>
                        <input type="number" id="assignmentWeightage" class="form-control weightage-input" min="0" max="100" value="15">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Quizzes (%)</label>
                        <input type="number" id="quizWeightage" class="form-control weightage-input" min="0" max="100" value="10">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Mid Term / OHT (%)</label>
                        <input type="number" id="midtermWeightage" class="form-control weightage-input" min="0" max="100" value="30">
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Finals (%)</label>
                        <input type="number" id="finalWeightage" class="form-control weightage-input" min="0" max="100" value="40">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Revisions (%)</label>
                        <input type="number" id="revisionWeightage" class="form-control weightage-input" min="0" max="100" value="5">
                    </div>
                </div>
                <div class="col-md-4">
                    <!-- This div intentionally left empty for layout balance -->
                </div>
            </div>

            <div class="mt-3">
                <div class="alert alert-info">
                    Total: <span id="totalWeightage">100</span>%
                </div>
                <button id="saveWeightagesBtn" class="btn btn-primary">Save Weightages</button>
            </div>
        </div>

        <!-- Add Marks -->
        <div id="addMarks" class="card mb-4 d-none">
            <h3>Add Marks</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Category</label>
                        <select id="markCategory" class="form-select">
                            <option value="assignment">Assignment</option>
                            <option value="quiz">Quiz</option>
                            <option value="midterm">Mid Term / OHT</option>
                            <option value="final">Final</option>
                            <option value="revision">Revision</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Title (Optional)</label>
                        <input type="text" id="markTitle" class="form-control" placeholder="e.g., Quiz 1">
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Obtained Marks</label>
                        <input type="number" id="obtainedMarks" class="form-control" min="0" step="0.01">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Total Marks</label>
                        <input type="number" id="totalMarks" class="form-control" min="0.01" step="0.01">
                    </div>
                </div>
            </div>

            <button id="addMarkBtn" class="btn btn-primary mt-3">Add Mark</button>
        </div>

        <!-- Existing Marks -->
        <div id="existingMarks" class="card d-none">
            <h3>Existing Marks</h3>
            <div class="accordion" id="marksAccordion">
                <!-- Categories will be added here -->
            </div>
        </div>
    </div>

    <!-- Theme Toggle Button -->
    <div class="theme-toggle" onclick="window.toggleTheme()">
        <span class="theme-icon">🌞</span>
        <span class="theme-text">Light Mode</span>
    </div>



    <!-- Main UI Module -->








</body>
</html>








</body>
</html>
    <!-- Scripts -->
    <script type="module" src="js/auth/init.js"></script>
    <script type="module" src="js/data/firestore-global.js"></script>
    <script type="module" src="js/academic/marks-integration.js"></script>
    <script type="module" src="js/data/sync-integration.js"></script>
    <script type="module" src="js/core/sync.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module" src="js/academic/marks-ui.js"></script>
    <script src="js/ui/side-drawer.js"></script>
    <script src="/js/core/inject-header.js"></script>
</body>
</html>