# CSS Optimization Phase 1: High Impact Combinations and Large File Breakdown
Write-Host "🎨 CSS Optimization Phase 1: High Impact Changes" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Create backups
Write-Host "Creating backups..." -ForegroundColor Cyan
if (-not (Test-Path "backups")) { New-Item -ItemType Directory -Path "backups" -Force }
if (-not (Test-Path "backups/css")) { New-Item -ItemType Directory -Path "backups/css" -Force }

Copy-Item "css" "backups/css-original" -Recurse -Force
Write-Host "✅ CSS files backed up to backups/css-original/" -ForegroundColor Green

# Phase 1.1: Fix Notification Duplication
Write-Host ""
Write-Host "📁 Phase 1.1: Fix Notification Style Duplication" -ForegroundColor Yellow
Write-Host "------------------------------------------------" -ForegroundColor Yellow

# Read theme.css and remove notification styles
if (Test-Path "css/core/theme.css") {
    $themeContent = Get-Content "css/core/theme.css" -Raw
    
    # Remove notification styles from theme.css (lines around 1614-1636)
    $themeContent = $themeContent -replace '(?s)/\*\*\s*\* General notification popup\..*?\.notification\.show \{[^}]*\}', ''
    
    Set-Content -Path "css/core/theme.css" -Value $themeContent -Encoding UTF8
    Write-Host "✅ Removed duplicate notification styles from theme.css" -ForegroundColor Green
}

# Enhance notifications.css to be comprehensive
$notificationContent = @'
/* Comprehensive Notification Styles */
:root {
    --notification-bg: var(--card-bg, #1e1e1e);
    --notification-text: var(--text-color, #ffffff);
    --notification-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    --notification-border-radius: 8px;
    --notification-z-index: 1100;
}

/* Base notification styles */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--notification-bg);
    color: var(--notification-text);
    padding: 15px 20px;
    border-radius: var(--notification-border-radius);
    box-shadow: var(--notification-shadow);
    z-index: var(--notification-z-index);
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    max-width: 400px;
    word-wrap: break-word;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

/* Notification types */
.notification.success {
    border-left: 4px solid #28a745;
}

.notification.error {
    border-left: 4px solid #dc3545;
}

.notification.info {
    border-left: 4px solid #17a2b8;
}

.notification.warning {
    border-left: 4px solid #ffc107;
}

/* Notification content */
.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification i {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.notification.success i {
    color: #28a745;
}

.notification.error i {
    color: #dc3545;
}

.notification.info i {
    color: #17a2b8;
}

.notification.warning i {
    color: #ffc107;
}

/* Task-specific notifications */
.navigation-notification,
.completion-notification,
.skip-notification {
    position: fixed;
    bottom: 80px;
    right: 20px;
    background-color: var(--notification-bg);
    color: var(--notification-text);
    padding: 12px 15px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.navigation-notification.show,
.completion-notification.show,
.skip-notification.show {
    opacity: 1;
    transform: translateY(0);
}

/* Auto-hide functionality */
.notification.auto-hide {
    animation: slideInAndOut 4s ease-in-out forwards;
}

@keyframes slideInAndOut {
    0% {
        transform: translateY(100px);
        opacity: 0;
    }
    15%, 85% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(100px);
        opacity: 0;
    }
}

/* Multiple notifications stacking */
.notification:nth-child(2) {
    bottom: 90px;
}

.notification:nth-child(3) {
    bottom: 160px;
}

.notification:nth-child(4) {
    bottom: 230px;
}
'@

Set-Content -Path "css/core/notifications.css" -Value $notificationContent -Encoding UTF8
Write-Host "✅ Enhanced css/core/notifications.css with comprehensive styles" -ForegroundColor Green

# Phase 1.2: Combine Study Spaces CSS
Write-Host ""
Write-Host "📁 Phase 1.2: Combine Study Spaces CSS Files" -ForegroundColor Yellow
Write-Host "---------------------------------------------" -ForegroundColor Yellow

if ((Test-Path "css/study/spaces.css") -and (Test-Path "css/study/spaces-alt.css")) {
    $spacesContent = Get-Content "css/study/spaces.css" -Raw
    $spacesAltContent = Get-Content "css/study/spaces-alt.css" -Raw
    
    $combinedSpacesContent = @"
/* Combined Study Spaces Styles */
/* Main spaces styles */
$spacesContent

/* Alternative/Additional spaces styles */
$spacesAltContent
"@
    
    Set-Content -Path "css/study/spaces.css" -Value $combinedSpacesContent -Encoding UTF8
    Remove-Item "css/study/spaces-alt.css" -Force
    Write-Host "✅ Combined study spaces CSS files" -ForegroundColor Green
}

# Phase 1.3: Move task-display.css to tasks directory
Write-Host ""
Write-Host "📁 Phase 1.3: Reorganize Task Display Styles" -ForegroundColor Yellow
Write-Host "---------------------------------------------" -ForegroundColor Yellow

if (Test-Path "css/layout/task-display.css") {
    $taskDisplayContent = Get-Content "css/layout/task-display.css" -Raw
    
    # Create or append to tasks components file
    if (Test-Path "css/tasks/general.css") {
        $tasksGeneralContent = Get-Content "css/tasks/general.css" -Raw
        $combinedTasksContent = @"
$tasksGeneralContent

/* Task Display Components (moved from layout) */
$taskDisplayContent
"@
        Set-Content -Path "css/tasks/general.css" -Value $combinedTasksContent -Encoding UTF8
    } else {
        Set-Content -Path "css/tasks/components.css" -Value $taskDisplayContent -Encoding UTF8
    }
    
    Remove-Item "css/layout/task-display.css" -Force
    Write-Host "✅ Moved task-display.css to tasks directory" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 CSS Optimization Phase 1 Completed!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host "✅ Fixed notification style duplication" -ForegroundColor White
Write-Host "✅ Combined study spaces CSS files" -ForegroundColor White
Write-Host "✅ Reorganized task display styles" -ForegroundColor White
Write-Host "✅ All original files backed up" -ForegroundColor White
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Test the optimized CSS files" -ForegroundColor White
Write-Host "2. Update HTML file references if needed" -ForegroundColor White
Write-Host "3. Consider Phase 2 for large file breakdown" -ForegroundColor White
