#!/bin/bash

# Remove Empty Folders Script
# This script safely removes empty directories while preserving important system folders

echo "Starting empty folder cleanup..."

# First, let's identify all empty directories (excluding Git and VS Code)
echo "=== IDENTIFYING EMPTY DIRECTORIES ==="
empty_dirs=$(find . -type d -empty 2>/dev/null | grep -v "\.git" | grep -v "\.vs" | sort)

if [ -z "$empty_dirs" ]; then
    echo "No empty directories found to remove."
    exit 0
fi

echo "Found the following empty directories:"
echo "$empty_dirs"

echo ""
echo "=== ANALYZING DIRECTORIES ==="

# Categorize directories for safe removal
safe_to_remove=()
keep_directories=()

while IFS= read -r dir; do
    case "$dir" in
        # Skip Git and VS Code directories
        */.git/* | */.vs/*)
            keep_directories+=("$dir")
            echo "KEEP (system): $dir"
            ;;
        # Skip if it might be important for build/deployment
        ./node_modules/* | ./dist/* | ./build/*)
            keep_directories+=("$dir")
            echo "KEEP (build): $dir"
            ;;
        # These are safe to remove - created by our reorganization
        ./public/js | ./scripts | ./styles | ./workers)
            safe_to_remove+=("$dir")
            echo "REMOVE (cleanup): $dir"
            ;;
        # These might be intentionally empty for future use
        ./assets/audio | ./icons | ./temp)
            safe_to_remove+=("$dir")
            echo "REMOVE (unused): $dir"
            ;;
        # These are from our JS reorganization
        ./js/simulation)
            safe_to_remove+=("$dir")
            echo "REMOVE (empty module): $dir"
            ;;
        # Flashcard directories that might be empty
        ./flashcards/css | ./flashcards/js)
            safe_to_remove+=("$dir")
            echo "REMOVE (empty flashcard dirs): $dir"
            ;;
        # Default: be cautious
        *)
            keep_directories+=("$dir")
            echo "KEEP (cautious): $dir"
            ;;
    esac
done <<< "$empty_dirs"

echo ""
echo "=== REMOVAL SUMMARY ==="
echo "Directories to remove: ${#safe_to_remove[@]}"
echo "Directories to keep: ${#keep_directories[@]}"

if [ ${#safe_to_remove[@]} -eq 0 ]; then
    echo "No directories marked for safe removal."
    exit 0
fi

echo ""
echo "=== REMOVING EMPTY DIRECTORIES ==="

removed_count=0
failed_count=0

for dir in "${safe_to_remove[@]}"; do
    if [ -d "$dir" ]; then
        # Double-check it's still empty before removing
        if [ -z "$(ls -A "$dir" 2>/dev/null)" ]; then
            if rmdir "$dir" 2>/dev/null; then
                echo "✓ Removed: $dir"
                ((removed_count++))
            else
                echo "✗ Failed to remove: $dir"
                ((failed_count++))
            fi
        else
            echo "⚠ Skipped (not empty): $dir"
        fi
    else
        echo "⚠ Already removed: $dir"
    fi
done

echo ""
echo "=== CLEANUP RESULTS ==="
echo "Successfully removed: $removed_count directories"
echo "Failed to remove: $failed_count directories"
echo "Kept for safety: ${#keep_directories[@]} directories"

# Final verification - check for any remaining empty directories
echo ""
echo "=== FINAL VERIFICATION ==="
remaining_empty=$(find . -type d -empty 2>/dev/null | grep -v "\.git" | grep -v "\.vs" | wc -l)
echo "Remaining empty directories (excluding .git/.vs): $remaining_empty"

if [ "$remaining_empty" -gt 0 ]; then
    echo ""
    echo "Remaining empty directories:"
    find . -type d -empty 2>/dev/null | grep -v "\.git" | grep -v "\.vs" | sort
fi

echo ""
echo "Empty folder cleanup completed!"
